'use server';

import { createClient } from '@/services/supabase/server';
import { getTenantSlug } from "@/services/tenant/";
import { InstructorStats } from '../types/types';
import { revalidatePath } from 'next/cache';

export async function getInstructorStats(): Promise<InstructorStats> {
  const defaultStats: InstructorStats = {
    total: 0,
    active: 0,
    inactive: 0,
    specialistCount: {},
    beltCount: {}
  };

  try {
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      console.error("Não foi possível obter o slug do tenant");
      return defaultStats;
    }

    const supabase = await createClient();
    
    // Primeiro obter o tenant_id a partir do slug
    const { data: tenantData, error: tenantError } = await supabase
      .from("tenants")
      .select("id")
      .eq("slug", tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error("Erro ao buscar o tenant:", tenantError);
      return defaultStats;
    }

    const tenantId = tenantData.id;

    // Buscar instrutores e suas informações do usuário
    // Note: instructors.user_id referencia auth.users, mas precisamos dos dados da tabela users(public)
    const { data: instructorsData, error: instructorsError } = await supabase
      .from("instructors")
      .select(`
        id,
        user_id,
        specialties,
        is_active
      `)
      .eq("tenant_id", tenantId);

    if (instructorsError) {
      console.error("Erro ao buscar instrutores:", instructorsError);
      return defaultStats;
    }

    if (!instructorsData || instructorsData.length === 0) {
      return defaultStats;
    }

    // Buscar informações dos usuários separadamente usando os user_ids dos instrutores
    const userIds = instructorsData.map(instructor => instructor.user_id);
    
    const { data: usersData, error: usersError } = await supabase
      .from("users")
      .select("id, status, role")
      .in("id", userIds)
      .eq("role", "instructor");

    if (usersError) {
      console.error("Erro ao buscar dados dos usuários:", usersError);
      return defaultStats;
    }

    // Criar mapa de user_id -> status para facilitar o lookup
    const userStatusMap = new Map<string, string>();
    usersData?.forEach(user => {
      userStatusMap.set(user.id, user.status);
    });

    // Calcular estatísticas usando is_active do instructor e status do user
    const total = instructorsData.length;
    let active = 0;
    
    instructorsData.forEach(instructor => {
      const userStatus = userStatusMap.get(instructor.user_id);
      // Consideramos ativo se tanto is_active do instructor quanto status do user estão ativos
      if (instructor.is_active && userStatus === 'active') {
        active++;
      }
    });
    
    const inactive = total - active;

    // Contar especialidades
    const specialistCount: Record<string, number> = {};
    instructorsData.forEach(instructor => {
      if (instructor.specialties && Array.isArray(instructor.specialties)) {
        instructor.specialties.forEach(specialty => {
          specialistCount[specialty] = (specialistCount[specialty] || 0) + 1;
        });
      }
    });

    // Buscar contagem de faixas dos instrutores
    const { data: beltsData, error: beltsError } = await supabase
      .from("instructor_belts")
      .select("instructor_id, belt_color, awarded_at")
      .eq("tenant_id", tenantId)
      .order("awarded_at", { ascending: false });

    const beltCount: Record<string, number> = {};
    
    if (!beltsError && beltsData) {
      // Agrupar por instrutor para pegar apenas a faixa mais recente de cada um
      const latestBelts = new Map<string, string>();
      
      beltsData.forEach((belt: any) => {
        if (!latestBelts.has(belt.instructor_id)) {
          latestBelts.set(belt.instructor_id, belt.belt_color);
        }
      });
      
      // Contar as faixas atuais
      latestBelts.forEach((beltColor) => {
        beltCount[beltColor] = (beltCount[beltColor] || 0) + 1;
      });
    }

    return {
      total,
      active,
      inactive,
      specialistCount,
      beltCount
    };

  } catch (error) {
    console.error("Erro ao buscar estatísticas dos instrutores:", error);
    return defaultStats;
  }
}

export async function refreshInstructorStats() {
  revalidatePath('/instrutores');
} 