"use server";

import { createClient } from '@/services/supabase/server';
import { getTenantSlug } from "@/services/tenant/";
import { Branch, BeltOption } from '../../types/types';

export async function getBranches(): Promise<Branch[]> {
  try {
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      console.error("Não foi possível obter o slug do tenant");
      return [];
    }

    // Primeiro obter o tenant_id a partir do slug
    const supabase = await createClient();
    const { data: tenantData, error: tenantError } = await supabase
      .from("tenants")
      .select("id")
      .eq("slug", tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error("Erro ao buscar o tenant:", tenantError);
      return [];
    }

    const tenantId = tenantData.id;

    // Agora buscar as filiais do tenant
    const { data: branchesData, error: branchesError } = await supabase
      .from("branches")
      .select("id, name, address, is_main")
      .eq("tenant_id", tenantId)
      .order("name");

    if (branchesError) {
      console.error("Erro ao buscar filiais:", branchesError);
      return [];
    }

    return branchesData?.map(branch => ({
      id: branch.id,
      name: branch.name,
      address: branch.address,
      active: true
    })) || [];
  } catch (error) {
    console.error("Erro ao buscar filiais:", error);
    return [];
  }
}

export async function getBeltOptions(): Promise<BeltOption[]> {
  try {
    const tenantSlug = await getTenantSlug();
    if (!tenantSlug) {
      console.error("Não foi possível obter o slug do tenant");
      return [];
    }

    const supabase = await createClient();
    
    // Primeiro obter o tenant_id a partir do slug
    const { data: tenantData, error: tenantError } = await supabase
      .from("tenants")
      .select("id")
      .eq("slug", tenantSlug)
      .single();

    if (tenantError || !tenantData) {
      console.error("Erro ao buscar o tenant:", tenantError);
      return [];
    }

    const tenantId = tenantData.id;
    
    // Buscar todas as faixas dos instrutores com informações do belt_level
    const { data, error } = await supabase
      .from('instructor_belts')
      .select(`
        instructor_id, 
        awarded_at,
        belt_level:belt_levels(
          id,
          belt_color,
          degree,
          label,
          sort_order
        )
      `)
      .eq('tenant_id', tenantId)
      .order('awarded_at', { ascending: false });

    if (error) {
      console.error('Erro ao buscar faixas dos instrutores:', error);
      return [];
    }

    // Agrupar por instrutor para pegar apenas a faixa mais recente
    const latestBelts = new Map<string, any>();
    
    if (data) {
      data.forEach((belt: any) => {
        if (!latestBelts.has(belt.instructor_id) && belt.belt_level) {
          latestBelts.set(belt.instructor_id, belt.belt_level);
        }
      });
    }

    // Agrupar por faixa e contar
    const beltCounts = new Map<string, { label: string; color: string; sort_order: number; count: number }>();

    latestBelts.forEach((beltLevel) => {
      const key = `${beltLevel.belt_color}-${beltLevel.degree}`;
      if (beltCounts.has(key)) {
        beltCounts.get(key)!.count++;
      } else {
        beltCounts.set(key, {
          label: beltLevel.label,
          color: beltLevel.belt_color,
          sort_order: beltLevel.sort_order,
          count: 1
        });
      }
    });

    // Converter para array e ordenar por sort_order
    return Array.from(beltCounts.entries())
      .map(([key, data]) => ({
        value: key,
        label: `${data.label} (${data.count})`,
        count: data.count
      }))
      .sort((a, b) => {
        const aData = beltCounts.get(a.value)!;
        const bData = beltCounts.get(b.value)!;
        return aData.sort_order - bData.sort_order;
      });

  } catch (error) {
    console.error('Erro ao buscar opções de faixas:', error);
    return [];
  }
}