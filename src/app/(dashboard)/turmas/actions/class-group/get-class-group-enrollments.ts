"use server";

import { createAdminClient } from "@/services/supabase/server";
import { validateUserAuthentication, validateClassGroup } from "./shared/validation-helpers";

/**
 * Obtém as matrículas de uma turma com detalhes dos alunos
 */
export async function getClassGroupEnrollments(groupId: string, searchParams?: { 
  status?: string; 
  search?: string; 
  page?: string; 
  limit?: string; 
  sort?: string;
  order?: string;
}) {
  try {
    const authResult = await validateUserAuthentication();
    if (!authResult.success) {
      return authResult;
    }

    const { tenantId } = authResult;

    // Verificar se a turma existe
    const classGroupValidation = await validateClassGroup(groupId, tenantId);
    if (!classGroupValidation.success) {
      return classGroupValidation;
    }

    const supabase = await createAdminClient();

    // Buscar matrículas com dados dos estudantes (SEM aplicar filtro de status aqui)
    let query = supabase
      .from("class_group_enrollments")
      .select(`
        id,
        status,
        enrollment_date,
        notes,
        class_group_id,
        students(
          id,
          check_in_code,
          users!students_user_id_fkey(
            id,
            first_name,
            last_name,
            full_name,
            email,
            phone,
            avatar_url
          ),
          current_belt_id
        )
      `)
      .eq("class_group_id", groupId)
      .eq("tenant_id", tenantId);

    // Ordenação padrão por data de matrícula
    query = query.order("enrollment_date", { ascending: false });

    const { data: enrollments, error } = await query;

    if (error) {
      console.error("Erro ao buscar matrículas:", error);
      return { success: false, errors: { _form: "Erro ao buscar matrículas" } };
    }

    if (!enrollments || enrollments.length === 0) {
      return { success: true, data: [] };
    }

    // Buscar informações de pausas para todas as matrículas
    const enrollmentIds = enrollments.map((e: any) => e.id);
    
    const { data: pausesData, error: pausesError } = await supabase
      .from("enrollment_pauses")
      .select(`
        id,
        enrollment_id,
        paused_at,
        resumed_at,
        reason,
        notes
      `)
      .in("enrollment_id", enrollmentIds)
      .eq("tenant_id", tenantId)
      .is("resumed_at", null) // Apenas pausas ativas (não retomadas)
      .order("paused_at", { ascending: false });

    if (pausesError) {
      console.error("Erro ao buscar pausas:", pausesError);
      // Continuar sem as informações de pausa se houver erro
    }

    // Mapear pausas por enrollment_id
    const pausesByEnrollment = new Map();
    if (pausesData) {
      pausesData.forEach((pause: any) => {
        pausesByEnrollment.set(pause.enrollment_id, pause);
      });
    }

    // Processar matrículas adicionando informações de pausa
    let processedEnrollments = enrollments.map((enrollment: any) => {
      const pause = pausesByEnrollment.get(enrollment.id);
      const isPaused = pause !== undefined;
      
      return {
        ...enrollment,
        is_paused: isPaused,
        pause_info: pause || null,
        // Adicionar o user diretamente no nível superior para compatibilidade
        user: enrollment.students?.users || null,
        // Adicionar informação de faixa
        belt: null // placeholder, substituído abaixo
      };
    });

    // Enriquecer faixas via RPC
    const supabaseClient = supabase; // alias
    await Promise.all(
      processedEnrollments.map(async (enrollment:any) => {
        const sid = enrollment.students?.id;
        if (!sid) return;
        const { data: beltDetails } = await supabaseClient.rpc('get_student_current_belt_details', { student_id_param: sid });
        if (beltDetails && beltDetails.length > 0) {
          const belt = beltDetails[0];
          enrollment.belt = belt.belt_color;
          enrollment.students.current_belt = {
            belt_level_id: belt.belt_level_id,
            color: belt.belt_color,
            degree: belt.degree,
            stripeColor: belt.stripe_color,
            showCenterLine: belt.show_center_line,
            centerLineColor: belt.center_line_color,
            label: belt.label,
            sortOrder: belt.sort_order,
          };
        }
      })
    );

    /* -------------------------------------------------------------
     * ORDENAÇÃO DINÂMICA (client-side)                         
     * -----------------------------------------------------------*/
    const sortField = searchParams?.sort || 'name';
    const orderFactor = searchParams?.order === 'desc' ? -1 : 1;

    processedEnrollments.sort((a: any, b: any) => {
      switch (sortField) {
        case 'name': {
          const aName = (a.user?.full_name || '').trim();
          const bName = (b.user?.full_name || '').trim();
          return aName.localeCompare(bName, 'pt-BR', { sensitivity: 'base' }) * orderFactor;
        }
        case 'belt': {
          const aBelt = (a.belt || '').toString();
          const bBelt = (b.belt || '').toString();
          return aBelt.localeCompare(bBelt, undefined, { sensitivity: 'base' }) * orderFactor;
        }
        case 'enrollment_date': {
          const aTime = new Date(a.enrollment_date).getTime();
          const bTime = new Date(b.enrollment_date).getTime();
          return (aTime - bTime) * orderFactor;
        }
        case 'status': {
          const statusOrder: Record<string, number> = {
            active: 1,
            paused: 2,
            inactive: 3,
            suspended: 4,
            completed: 5,
          };
          const aStatus = a.is_paused ? 'paused' : a.status;
          const bStatus = b.is_paused ? 'paused' : b.status;
          return (statusOrder[aStatus] - statusOrder[bStatus]) * orderFactor;
        }
        default:
          return 0;
      }
    });

    // AGORA aplicar filtro de status considerando pausas (após carregar informações de pausa)
    if (searchParams?.status) {
      if (searchParams.status === 'paused') {
        processedEnrollments = processedEnrollments.filter((enrollment: any) => enrollment.is_paused);
      } else if (searchParams.status === 'active') {
        processedEnrollments = processedEnrollments.filter((enrollment: any) => 
          enrollment.status === 'active' && !enrollment.is_paused
        );
      } else {
        processedEnrollments = processedEnrollments.filter((enrollment: any) => 
          enrollment.status === searchParams.status && !enrollment.is_paused
        );
      }
    }

    // Aplicar filtro de busca no lado do cliente se fornecido
    if (searchParams?.search) {
      const searchTerm = searchParams.search.toLowerCase();
      processedEnrollments = processedEnrollments.filter((enrollment: any) => {
        const user = enrollment.user;
        if (!user) return false;
        
        return (
          user.full_name?.toLowerCase().includes(searchTerm) ||
          user.first_name?.toLowerCase().includes(searchTerm) ||
          user.last_name?.toLowerCase().includes(searchTerm) ||
          user.email?.toLowerCase().includes(searchTerm)
        );
      });
    }

    return { success: true, data: processedEnrollments };
  } catch (error) {
    console.error("Erro ao buscar matrículas:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
} 