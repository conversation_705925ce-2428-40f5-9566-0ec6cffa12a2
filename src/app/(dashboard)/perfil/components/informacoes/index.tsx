'use client'

import { Card } from '@/components/ui/card'
import { format } from 'date-fns'
import { ptBR } from 'date-fns/locale'
import { User, Calendar, Heart, CreditCard, FileText, Info, AlertCircle, HeartPulse, Pill, Award, Star, Clock } from 'lucide-react'
import { useState, useEffect } from 'react'
import { EditableField } from '../editable-field'
import { JiuJitsuBelt } from '@/components/belt'
import { useQuery } from '@tanstack/react-query'
import { loadUserProfileCache } from '@/services/user/user-profile-cache'
import { CACHE_KEYS } from '@/constants/cache-keys'
import { cacheService } from '@/services/cache'
import { formatContractType, formatPaymentModel } from '@/utils/format-utils'
import { formatDateBrazil } from '@/utils/format'

interface InformacoesTabProps {
  role?: string
  userData?: any
  userId?: string
}

interface BeltData {
  id: string
  color: string
  degree: number
  awardedAt: string | null
  instructor?: string | null
  stripeColor?: string | null
  showCenterLine?: boolean
  centerLineColor?: string | null
  label?: string | null
}

interface PaymentData {
  lastPaymentAmount: number
  lastPaymentDate: string | null
  nextPaymentDue: string | null
  paymentStatus: 'em_dia' | 'atrasado' | string
  isLoading: boolean
  error: string | null
}

interface CurrentPlanData {
  id: string
  membership_id: string
  title: string
  status: string
  price: number
  billing_period: string
  start_date: string | null
  end_date: string | null
  next_billing_date: string | null
  modalities: string[]
  pricing_config: any
}

export function InformacoesTab({ role = 'student', userData: initialUserData, userId }: InformacoesTabProps) {
  const isAdmin = role === 'admin'
  const isInstructor = role === 'instructor'

  const { data: userData, isLoading } = useQuery({
    queryKey: [CACHE_KEYS.USER_PROFILE[0], userId],
    queryFn: async () => {
      if (!userId && !initialUserData) return null

      if (initialUserData) {
        if (isAdmin && initialUserData.role === 'student') {
          return {
            ...initialUserData,
            birthDate: initialUserData.birth_date || initialUserData.birthDate || initialUserData.metadata?.birthDate,
            gender: initialUserData.gender || initialUserData.metadata?.gender
          }
        }
        return initialUserData
      }

      const cachedData = userId ? cacheService.getData<any>([CACHE_KEYS.USER_PROFILE[0], userId]) : undefined
      if (cachedData) {
        return cachedData
      }

      const response = await loadUserProfileCache(userId as string)
      if (response.error || !response.data) {
        throw new Error(response.error || 'Erro ao carregar dados do perfil')
      }

      if (userId) {
        cacheService.setData([CACHE_KEYS.USER_PROFILE[0], userId], response.data)
      }

      return response.data
    },
    staleTime: Infinity,
    gcTime: 30 * 60 * 1000,
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: !!userId || !!initialUserData,
    initialData: () => {
      if (initialUserData) return initialUserData

      return userId ? cacheService.getData<any>([CACHE_KEYS.USER_PROFILE[0], userId]) : undefined
    }
  })

  // Estado para armazenar os dados da faixa
  const [currentBelt, setCurrentBelt] = useState<BeltData | null>(null)
  const [beltLoading, setBeltLoading] = useState(false)

  // Estado específico para os dados de pagamento
  const [paymentData, setPaymentData] = useState<PaymentData>({
    lastPaymentAmount: 0,
    lastPaymentDate: null,
    nextPaymentDue: null,
    paymentStatus: '',
    isLoading: true,
    error: null
  })

  // Estado para dados do plano atual
  const [currentPlan, setCurrentPlan] = useState<CurrentPlanData | null>(null)
  const [planLoading, setPlanLoading] = useState(false)

  // Buscar dados da faixa diretamente com fetch
  useEffect(() => {
    async function fetchBeltData() {
      if (!userData?.id) {
        console.log('ID do usuário não disponível, abortando fetch')
        return
      }

      // Tentar encontrar o ID da faixa em diferentes campos possíveis
      const beltId = userData?.currentBeltId || userData?.current_belt_id || userData?.metadata?.currentBeltId || userData?.currentBelt?.id

      if (!beltId) {
        console.log('ID da faixa não disponível em nenhum campo conhecido, abortando fetch')
        return
      }

      if (isAdmin) {
        console.log('Usuário é admin, abortando fetch')
        return
      }

      setBeltLoading(true)
      console.log(`[API] Buscando detalhes da faixa para o usuário ${userData.id}, faixa: ${beltId}`)

      try {
        const response = await fetch(`/api/user/${userData.id}/belts/details?beltId=${beltId}`, {
          cache: 'force-cache',
          next: {
            revalidate: 300 // 5 minutos, igual ao valor definido na API
          }
        })

        if (!response.ok) {
          console.error('Erro ao buscar dados da faixa:', await response.text())
          setBeltLoading(false)
          return
        }

        const data = await response.json()
        console.log('[API] Dados da faixa recebidos:', data)
        setCurrentBelt(data)
      } catch (error) {
        console.error('[API] Erro ao buscar dados da faixa:', error)
      } finally {
        setBeltLoading(false)
      }
    }

    if (userData) {
      fetchBeltData()
    }
  }, [userData, isAdmin])

  // useEffect para buscar dados do plano atual e informações de pagamento
  useEffect(() => {
    async function fetchCurrentPlanAndPaymentData() {
      if (!userData?.id || isAdmin) {
        setPaymentData((prev) => ({ ...prev, isLoading: false }))
        setPlanLoading(false)
        return
      }

      setPaymentData((prev) => ({ ...prev, isLoading: true }))
      setPlanLoading(true)
      console.log(`[API] Buscando plano atual e informações de pagamento para o usuário ${userData.id}`)

      try {
        // Buscar plano atual primeiro
        const currentPlanResponse = await fetch(`/api/user/${userData.id}/current-plan`, {
          cache: 'no-store'
        })

        let planData = null
        if (currentPlanResponse.ok) {
          planData = await currentPlanResponse.json()
          setCurrentPlan(planData)
          console.log('[API] Dados do plano atual recebidos:', planData)
        } else {
          console.log('[API] Nenhum plano ativo encontrado')
          setCurrentPlan(null)
        }

        // Buscar informações de pagamento
        const paymentResponse = await fetch(`/api/user/${userData.id}/payments/info`, {
          cache: 'no-store'
        })

        if (!paymentResponse.ok) {
          throw new Error(`Erro ao buscar dados de pagamento: ${paymentResponse.status}`)
        }

        const paymentData = await paymentResponse.json()
        console.log('[API] Dados de pagamento recebidos:', paymentData)

        // Se temos dados do plano atual, usar o preço do plano, senão usar subscription_amount
        const planPrice = planData?.price || 0
        const paymentAmount = planPrice > 0 ? planPrice : (paymentData.subscription_amount || 0)

        setPaymentData({
          lastPaymentAmount: paymentAmount,
          lastPaymentDate: paymentData.last_payment_date,
          nextPaymentDue: paymentData.next_payment_due,
          paymentStatus: paymentData.payment_status || 'desconhecido',
          isLoading: false,
          error: null
        })
      } catch (error) {
        console.error('[API] Erro ao buscar dados:', error)
        setPaymentData((prev) => ({
          ...prev,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Erro ao carregar dados de pagamento'
        }))
      } finally {
        setPlanLoading(false)
      }
    }

    if (userData) {
      fetchCurrentPlanAndPaymentData()
    }
  }, [userData, isAdmin])

  const beltToDisplay = currentBelt || userData?.currentBelt || userData?.current_belt
  const awardedDate = beltToDisplay?.awardedAt || (beltToDisplay?.awarded_at ? format(new Date(beltToDisplay.awarded_at), 'dd/MM/yyyy', { locale: ptBR }) : null)
  const instructorName = beltToDisplay?.instructor || beltToDisplay?.instructor_name

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm bg-white dark:bg-slate-800 p-8 animate-pulse">
          <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded mb-4"></div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4 mb-3"></div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2"></div>
        </Card>
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm bg-white dark:bg-slate-800 p-8 animate-pulse">
          <div className="h-6 bg-slate-200 dark:bg-slate-700 rounded mb-4"></div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4 mb-3"></div>
          <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-1/2"></div>
        </Card>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 p-6 bg-slate-50 dark:bg-slate-900 rounded-b-lg">
      {/* Informações Pessoais */}
      <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
        <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
            <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
            Informações Pessoais
          </h2>
        </div>

        <div className="divide-y divide-slate-100 dark:divide-slate-800">
          <div className="px-6 py-4 flex items-start">
            <div className="flex-shrink-0 mt-0.5 mr-3">
              <Calendar className="w-5 h-5 text-slate-400 dark:text-slate-500" />
            </div>

            <div className="flex-grow">
              <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Data de Nascimento</p>
              <EditableField label="" value={userData?.birthDate || userData?.birth_date || userData?.metadata?.birthDate || ''} fieldName="birthDate" type="date" userId={userId} inline={true} className="mt-1 text-sm text-slate-900 dark:text-slate-100" />
            </div>
          </div>

          <div className="px-6 py-4 flex items-start">
            <div className="flex-shrink-0 mt-0.5 mr-3">
              <User className="w-5 h-5 text-slate-400 dark:text-slate-500" />
            </div>

            <div className="flex-grow">
              <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Gênero</p>
              <EditableField
                label=""
                value={userData?.gender || userData?.metadata?.gender || ''}
                fieldName="gender"
                type="select"
                options={[
                  { value: 'masculino', label: 'Masculino' },
                  { value: 'feminino', label: 'Feminino' },
                  { value: 'outro', label: 'Outro' },
                  { value: 'prefiro_nao_informar', label: 'Prefiro não informar' }
                ]}
                userId={userId}
                inline={true}
                className="mt-1 text-sm text-slate-900 dark:text-slate-100"
              />
            </div>
          </div>

          {/* Para estudantes: matrícula / Para instrutores: registro de federação */}
          {!isAdmin && (
            <div className="px-6 py-4 flex items-start">
              <div className="flex-shrink-0 mt-0.5 mr-3">
                <FileText className="w-5 h-5 text-slate-400 dark:text-slate-500" />
              </div>

              <div className="flex-grow">
                <p className="text-sm font-medium text-slate-500 dark:text-slate-400">{isInstructor ? 'Registro de Federação' : 'Matrícula'}</p>
                <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{isInstructor ? userData?.federation_registration || 'Não informado' : userData?.registrationNumber || userData?.registration_number || 'Não informado'}</p>
              </div>
            </div>
          )}

          <div className="px-6 py-4 flex items-start">
            <div className="flex-shrink-0 mt-0.5 mr-3">
              <Calendar className="w-5 h-5 text-slate-400 dark:text-slate-500" />
            </div>

            <div className="flex-grow">
              <p className="text-sm font-medium text-slate-500 dark:text-slate-400">{isInstructor ? 'Contratado em' : 'Membro desde'}</p>
              <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{isInstructor && userData?.hire_date ? formatDateBrazil(userData.hire_date) : userData?.joinDate || 'Não informado'}</p>
            </div>
          </div>
        </div>
      </Card>

      {/* Observações Médicas (apenas para estudantes) */}
      {!isAdmin && !isInstructor && (
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
          <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
              <Heart className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
              Observações Médicas
            </h2>
          </div>

          <div className="p-6">
            {userData?.healthNotes || userData?.allergies || userData?.medicalConditions || userData?.health_notes || userData?.medical_conditions ? (
              <div className="space-y-4">
                <EditableField label="Observações Gerais" value={userData?.healthNotes || userData?.health_notes || ''} icon={<Info className="w-4 h-4 text-slate-400 dark:text-slate-500" />} fieldName="healthNotes" userId={userId} />

                <EditableField label="Alergias" value={userData?.allergies || ''} icon={<AlertCircle className="w-4 h-4 text-amber-500 dark:text-amber-400" />} fieldName="allergies" userId={userId} />

                <EditableField label="Condições Médicas" value={userData?.medicalConditions || userData?.medical_conditions || ''} icon={<HeartPulse className="w-4 h-4 text-rose-500 dark:text-rose-400" />} fieldName="medicalConditions" userId={userId} />

                <EditableField label="Medicamentos" value={userData?.medications || ''} icon={<Pill className="w-4 h-4 text-blue-500 dark:text-blue-400" />} fieldName="medications" userId={userId} />
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-4 text-center">
                <HeartPulse className="w-10 h-10 text-slate-300 dark:text-slate-600 mb-2" />
                <p className="text-sm text-slate-500 dark:text-slate-400">Nenhuma observação médica registrada.</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Graduação Atual - para estudantes e instrutores */}
      {!isAdmin && (
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
          <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
              <Award className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
              Graduação Atual
            </h2>
          </div>

          <div className="p-6">
            {/* Exibir graduação para estudantes */}
            {!isInstructor && beltToDisplay ? (
              <div className="flex items-center gap-4">
                <JiuJitsuBelt beltColor={beltToDisplay.color || beltToDisplay.belt_color || '#FFFFFF'} degrees={beltToDisplay.degree || 0} className="w-48 h-12" stripeColor={beltToDisplay.stripeColor || beltToDisplay.stripe_color} showCenterLine={beltToDisplay.showCenterLine || beltToDisplay.show_center_line} centerLineColor={beltToDisplay.centerLineColor || beltToDisplay.center_line_color} />
                <div>
                  <p className="text-base font-medium text-slate-900 dark:text-slate-100">
                    {beltToDisplay.modality_name || 'Sem modalidade'} - {beltToDisplay.label || beltToDisplay.belt_label || 'Sem faixa'}
                  </p>
                  {awardedDate && (
                    <div className="flex items-center mt-2 text-sm text-slate-500 dark:text-slate-400">
                      <Calendar className="w-4 h-4 mr-1.5" />
                      Graduado em {awardedDate}
                    </div>
                  )}
                  {instructorName && (
                    <div className="flex items-center mt-1 text-sm text-slate-500 dark:text-slate-400">
                      <User className="w-4 h-4 mr-1.5" />
                      {instructorName}
                    </div>
                  )}
                </div>
              </div>
            ) : /* Exibir graduação para instrutores */ isInstructor && userData?.current_instructor_belt ? (
              <div className="flex items-center gap-4">
                <JiuJitsuBelt beltColor={userData.current_instructor_belt.color || userData.current_instructor_belt.belt_color || '#FFFFFF'} degrees={userData.current_instructor_belt.degree || 0} className="w-48 h-12" stripeColor={userData.current_instructor_belt.stripeColor || userData.current_instructor_belt.stripe_color} showCenterLine={userData.current_instructor_belt.showCenterLine || userData.current_instructor_belt.show_center_line} centerLineColor={userData.current_instructor_belt.centerLineColor || userData.current_instructor_belt.center_line_color} />
                <div>
                  <p className="text-base font-medium text-slate-900 dark:text-slate-100">
                    {userData.current_instructor_belt.modality_name || 'Modalidade Desconhecida'}
                    {userData.current_instructor_belt.degree ? ` - Faixa ${userData.current_instructor_belt.degree}° grau` : ' - Faixa'}
                  </p>
                  {userData.current_instructor_belt.awarded_at && (
                    <div className="flex items-center mt-2 text-sm text-slate-500 dark:text-slate-400">
                      <Calendar className="w-4 h-4 mr-1.5" />
                      Graduado em {formatDateBrazil(userData.current_instructor_belt.awarded_at)}
                    </div>
                  )}
                  {(userData.current_instructor_belt.awarded_by_user || userData.current_instructor_belt.awarded_by_full_name || userData.current_instructor_belt.awarded_by_first_name) && (
                    <div className="flex items-center mt-1 text-sm text-slate-500 dark:text-slate-400">
                      <User className="w-4 h-4 mr-1.5" />
                      Professor: {userData.current_instructor_belt.awarded_by_user?.full_name || userData.current_instructor_belt.awarded_by_full_name || (userData.current_instructor_belt.awarded_by_user?.first_name && userData.current_instructor_belt.awarded_by_user?.last_name ? `${userData.current_instructor_belt.awarded_by_user.first_name} ${userData.current_instructor_belt.awarded_by_user.last_name}` : userData.current_instructor_belt.awarded_by_first_name && userData.current_instructor_belt.awarded_by_last_name ? `${userData.current_instructor_belt.awarded_by_first_name} ${userData.current_instructor_belt.awarded_by_last_name}` : 'Não informado')}
                    </div>
                  )}
                  {userData.current_instructor_belt.notes && <div className="mt-2 text-sm text-slate-600 dark:text-slate-400 italic">"{userData.current_instructor_belt.notes}"</div>}
                </div>
              </div>
            ) : beltLoading ? (
              <div className="flex flex-col items-center justify-center py-4">
                <div className="w-8 h-8 border-2 border-primary/30 border-t-primary animate-spin rounded-full mb-2"></div>
                <p className="text-sm text-slate-500 dark:text-slate-400">Carregando graduação...</p>
              </div>
            ) : userData?.currentBeltId || isInstructor ? (
              <div className="flex flex-col items-center justify-center py-4 text-center">
                <Award className="w-10 h-10 text-amber-500 dark:text-amber-400 mb-2" />
                <p className="text-sm text-slate-500 dark:text-slate-400">Informações da graduação não disponíveis</p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-4 text-center">
                <Award className="w-10 h-10 text-slate-300 dark:text-slate-600 mb-2" />
                <p className="text-sm text-slate-500 dark:text-slate-400">Nenhuma graduação registrada</p>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Informações Específicas do Instrutor */}
      {isInstructor && (
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
          <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
              <User className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
              Informações Profissionais
            </h2>
          </div>

          <div className="divide-y divide-slate-100 dark:divide-slate-800">
            {Number(userData?.experience_years) > 0 && (
              <div className="px-6 py-4 flex items-start">
                <div className="flex-shrink-0 mt-0.5 mr-3">
                  <Award className="w-5 h-5 text-slate-400 dark:text-slate-500" />
                </div>
                <div className="flex-grow">
                  <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Anos de Experiência</p>
                  <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{Number(userData.experience_years)} anos</p>
                </div>
              </div>
            )}

            {userData?.certification_level && (
              <div className="px-6 py-4 flex items-start">
                <div className="flex-shrink-0 mt-0.5 mr-3">
                  <Award className="w-5 h-5 text-slate-400 dark:text-slate-500" />
                </div>
                <div className="flex-grow">
                  <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Nível de Certificação</p>
                  <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{userData.certification_level}</p>
                </div>
              </div>
            )}

            {userData?.specialties && userData.specialties.length > 0 && (
              <div className="px-6 py-4 flex items-start">
                <div className="flex-shrink-0 mt-0.5 mr-3">
                  <Star className="w-5 h-5 text-slate-400 dark:text-slate-500" />
                </div>
                <div className="flex-grow">
                  <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Especialidades</p>
                  <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{Array.isArray(userData.specialties) ? userData.specialties.join(', ') : userData.specialties}</p>
                </div>
              </div>
            )}

            {userData?.contract_type && (
              <div className="px-6 py-4 flex items-start">
                <div className="flex-shrink-0 mt-0.5 mr-3">
                  <FileText className="w-5 h-5 text-slate-400 dark:text-slate-500" />
                </div>
                <div className="flex-grow">
                  <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Tipo de Contrato</p>
                  <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{formatContractType(userData.contract_type)}</p>
                </div>
              </div>
            )}

            {userData?.hire_date && (
              <div className="px-6 py-4 flex items-start">
                <div className="flex-shrink-0 mt-0.5 mr-3">
                  <Calendar className="w-5 h-5 text-slate-400 dark:text-slate-500" />
                </div>
                <div className="flex-grow">
                  <p className="text-sm font-medium text-slate-500 dark:text-slate-400">Data de Contratação</p>
                  <p className="mt-1 text-sm text-slate-900 dark:text-slate-100">{formatDateBrazil(userData.hire_date)}</p>
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Pagamento (para estudantes) ou Informações Contratuais (para instrutores) */}
      {!isAdmin && (
        <Card className="overflow-hidden border-slate-200 dark:border-slate-700 shadow-sm hover:shadow transition-all duration-200 bg-white dark:bg-slate-800">
          <div className="bg-slate-50 dark:bg-slate-700/50 px-6 py-4 border-b border-slate-200 dark:border-slate-700">
            <h2 className="text-lg font-semibold text-slate-900 dark:text-slate-100 flex items-center">
              <CreditCard className="w-5 h-5 mr-2 text-slate-500 dark:text-slate-400" />
              {isInstructor ? 'Informações Contratuais' : 'Pagamento'}
            </h2>
          </div>

          <div className="p-6">
            {isInstructor ? (
              /* Informações contratuais para instrutores */
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">Tipo de Contrato</span>
                  <div className="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">{formatContractType(userData?.contract_type) || 'Não informado'}</div>
                </div>

                {userData?.payment_value && (
                  <div className="flex items-baseline">
                    <span className="text-3xl font-bold text-slate-900 dark:text-slate-100">R$ {Number(userData.payment_value || 0).toFixed(2)}</span>
                    <span className="ml-1 text-xs text-slate-500 dark:text-slate-400">/{userData?.payment_model === 'fixed' ? 'mês' : userData?.payment_model === 'hourly' ? 'hora' : userData?.payment_model === 'hora_aula' ? 'aula' : userData?.payment_model === 'commission' ? 'comissão' : 'período'}</span>
                  </div>
                )}

                <div className="grid grid-cols-2 gap-4 pt-2 border-t border-slate-100 dark:border-slate-700">
                  {userData?.payment_model && (
                    <div className="flex items-center text-sm">
                      <Clock className="w-4 h-4 mr-1.5 text-slate-400 dark:text-slate-500" />
                      <div>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Modelo de Pagamento</p>
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300">{formatPaymentModel(userData.payment_model)}</p>
                      </div>
                    </div>
                  )}

                  {userData?.hire_date && (
                    <div className="flex items-center text-sm">
                      <Calendar className="w-4 h-4 mr-1.5 text-slate-400 dark:text-slate-500" />
                      <div>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Data de Contratação</p>
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300">{formatDateBrazil(userData.hire_date)}</p>
                      </div>
                    </div>
                  )}
                </div>

                {userData?.payment_percentage && (
                  <div className="mt-3 pt-3 border-t border-slate-100 dark:border-slate-700">
                    <div className="flex items-center text-sm">
                      <div className="flex-grow">
                        <p className="text-xs text-slate-500 dark:text-slate-400">Porcentagem de Comissão</p>
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300">{userData.payment_percentage}%</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ) : /* Informações de pagamento para estudantes */
            paymentData.isLoading || planLoading ? (
              <div className="flex items-center justify-center h-24">
                <div className="w-6 h-6 border-2 border-primary/30 border-t-primary animate-spin rounded-full"></div>
              </div>
            ) : paymentData.error ? (
              <div className="flex items-center text-sm text-red-500 dark:text-red-400 h-24">
                <AlertCircle className="w-5 h-5 mr-2 flex-shrink-0" />
                <span>Erro ao carregar dados de pagamento</span>
              </div>
            ) : currentPlan ? (
              <div className="space-y-4">
                {/* Informações do plano atual */}
                {/* <div className="bg-slate-50 dark:bg-slate-700/30 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h3 className="text-sm font-semibold text-slate-900 dark:text-slate-100">{currentPlan.title}</h3>
                      <p className="text-xs text-slate-500 dark:text-slate-400">Plano {currentPlan.billing_period}</p>
                    </div>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      currentPlan.status === 'active'
                        ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300'
                        : 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300'
                    }`}>
                      {currentPlan.status === 'active' ? 'Ativo' : currentPlan.status}
                    </div>
                  </div>

                  {currentPlan.modalities && currentPlan.modalities.length > 0 && (
                    <div className="text-xs text-slate-600 dark:text-slate-400">
                      Modalidades: {currentPlan.modalities.join(', ')}
                    </div>
                  )}
                </div> */}

                {/* Status de pagamento */}
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-slate-500 dark:text-slate-400">Status do pagamento</span>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    paymentData.paymentStatus === 'em_dia'
                      ? 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/30 dark:text-emerald-300'
                      : paymentData.paymentStatus === 'atrasado'
                      ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      : 'bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300'
                  }`}>
                    {paymentData.paymentStatus === 'em_dia' ? 'Em dia' :
                     paymentData.paymentStatus === 'atrasado' ? 'Atrasado' :
                     paymentData.paymentStatus === 'pendente' ? 'Pendente' : 'Status desconhecido'}
                  </div>
                </div>

                {/* Valor da recorrência */}
                <div className="flex items-baseline">
                  <span className="text-3xl font-bold text-slate-900 dark:text-slate-100">R$ {Number(paymentData.lastPaymentAmount || 0).toFixed(2)}</span>
                  <span className="ml-1 text-xs text-slate-500 dark:text-slate-400">/{currentPlan.billing_period.toLowerCase()}</span>
                </div>

                {/* Datas importantes */}
                <div className="grid grid-cols-2 gap-2 pt-2 border-t border-slate-100 dark:border-slate-700">
                  {paymentData.lastPaymentDate && (
                    <div className="flex items-center text-sm">
                      <Calendar className="w-4 h-4 mr-1.5 text-slate-400 dark:text-slate-500" />
                      <div>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Último pagamento</p>
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300">{new Date(paymentData.lastPaymentDate).toLocaleDateString('pt-BR')}</p>
                      </div>
                    </div>
                  )}

                  {(paymentData.nextPaymentDue || currentPlan.next_billing_date) && (
                    <div className="flex items-center text-sm">
                      <Clock className="w-4 h-4 mr-1.5 text-slate-400 dark:text-slate-500" />
                      <div>
                        <p className="text-xs text-slate-500 dark:text-slate-400">Próximo vencimento</p>
                        <p className="text-sm font-medium text-slate-700 dark:text-slate-300">
                          {formatDateBrazil(paymentData.nextPaymentDue || currentPlan.next_billing_date || '')}
                        </p>
                      </div>
                    </div>
                  )}
                </div>

                {/* Período do plano */}
                {currentPlan.start_date && (
                  <div className="pt-2 border-t border-slate-100 dark:border-slate-700">
                    <div className="flex justify-between text-xs text-slate-500 dark:text-slate-400">
                      <span>Início: {formatDateBrazil(currentPlan.start_date)}</span>
                      {currentPlan.end_date && (
                        <span>Fim: {formatDateBrazil(currentPlan.end_date)}</span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <CreditCard className="w-10 h-10 text-slate-300 dark:text-slate-600 mb-2" />
                <p className="text-sm text-slate-500 dark:text-slate-400 mb-1">Nenhum plano ativo</p>
                <p className="text-xs text-slate-400 dark:text-slate-500">O aluno não possui um plano ativo no momento</p>
              </div>
            )}
          </div>
        </Card>
      )}
    </div>
  )
}
