'use client'

import { useState, useEffect, ReactNode, useMemo, useRef } from 'react'
import { Check, X, Edit2, Loader2 } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useEditMode } from './edit-context'
import { cn } from '@/lib/utils'
import { useFieldPermission } from '@/services/permissions'
import { CountrySelect } from '@/components/shared/PhoneInput/country-select'
import { Input } from '@/components/ui/input'
import { formatPhoneNumber, parsePhoneNumber, formatPhoneNumberBRWithDDI } from '@/utils/phone-utils'
import { formatNationalNumber } from '@/hooks/form/usePhoneFormat'
import { exceedsMaxDigits } from '@/components/shared/PhoneInput/phone-config'

export interface EditablePhoneFieldProps {
  label: string
  value: string
  fieldName: string
  placeholder?: string
  emptyText?: string
  className?: string
  validate?: (value: string) => string | undefined
  userId?: string
  inline?: boolean
  defaultCountryCode?: string
}

export function EditablePhoneField({
  label,
  value,
  fieldName,
  placeholder = 'Não informado',
  emptyText = 'Não informado',
  className,
  validate,
  userId,
  inline = false,
  defaultCountryCode = '+55'
}: EditablePhoneFieldProps) {
  const { setFieldValue, getCurrentValue } = useEditMode()
  const [isEditing, setIsEditing] = useState(false)
  const [error, setError] = useState<string | undefined>()
  const [isSaving, setIsSaving] = useState(false)
  const [wasRecentlyEdited, setWasRecentlyEdited] = useState(false)
  const { canEditField } = useFieldPermission()

  const lastUpdateTime = useRef<number>(0)
  
  const canEditThisField = userId ? canEditField(fieldName as any, userId) : false
  
  // Estado para controlar o valor durante a edição
  const currentValue = getCurrentValue(fieldName) || value
  
  const { countryCode, nationalNumber } = useMemo(
    () => parsePhoneNumber(currentValue, defaultCountryCode),
    [currentValue, defaultCountryCode]
  )
  
  const [selectedCountryCode, setSelectedCountryCode] = useState(countryCode)
  const [phoneWithoutCountryCode, setPhoneWithoutCountryCode] = useState(nationalNumber)

  useEffect(() => {
    if (wasRecentlyEdited) {
      const timer = setTimeout(() => {
        setWasRecentlyEdited(false)
      }, 2000)
      
      return () => clearTimeout(timer)
    }
  }, [wasRecentlyEdited])

  useEffect(() => {
    const handleProfileChanged = () => {
      if (isEditing) return
      
      const now = Date.now()
      if (now - lastUpdateTime.current < 200) return
      lastUpdateTime.current = now
      
      const updatedValue = getCurrentValue(fieldName)
      
      if (updatedValue !== undefined && updatedValue !== currentValue) {
        console.log(`[EditablePhoneField] Campo ${fieldName} atualizado via evento global:`, updatedValue)
        const { countryCode: newCountryCode, nationalNumber: newNationalNumber } = parsePhoneNumber(updatedValue, defaultCountryCode)
        setSelectedCountryCode(newCountryCode)
        setPhoneWithoutCountryCode(newNationalNumber)
      }
    }
    
    window.addEventListener('profile:updated', handleProfileChanged)
    window.addEventListener('app:profile-change', handleProfileChanged)
    
    return () => {
      window.removeEventListener('profile:updated', handleProfileChanged)
      window.removeEventListener('app:profile-change', handleProfileChanged)
    }
  }, [fieldName, currentValue, getCurrentValue, isEditing, defaultCountryCode])

  useEffect(() => {
    if (!isEditing) {
      const { countryCode: newCountryCode, nationalNumber: newNationalNumber } = parsePhoneNumber(currentValue, defaultCountryCode)
      setSelectedCountryCode(newCountryCode)
      setPhoneWithoutCountryCode(newNationalNumber)
    }
  }, [currentValue, isEditing, defaultCountryCode])

  const isExceedingLimit = useMemo(
    () => exceedsMaxDigits(selectedCountryCode, phoneWithoutCountryCode),
    [selectedCountryCode, phoneWithoutCountryCode]
  )

  const handleEdit = () => {
    if (canEditThisField) {
      const valueToEdit = getCurrentValue(fieldName) || value
      const { countryCode: editCountryCode, nationalNumber: editNationalNumber } = parsePhoneNumber(valueToEdit, defaultCountryCode)
      setSelectedCountryCode(editCountryCode)

      // Formata apenas o número nacional para exibição durante a edição
      const formattedNational = formatNationalNumber(editNationalNumber, editCountryCode)

      setPhoneWithoutCountryCode(formattedNational)
      setIsEditing(true)
    }
  }

  const handleCancel = () => {
    const valueToRevert = getCurrentValue(fieldName) || value
    const { countryCode: revertCountryCode, nationalNumber: revertNationalNumber } = parsePhoneNumber(valueToRevert, defaultCountryCode)
    setSelectedCountryCode(revertCountryCode)
    setPhoneWithoutCountryCode(revertNationalNumber)
    setError(undefined)
    setIsEditing(false)
  }

  const handleConfirm = async () => {
    const fullPhoneNumber = phoneWithoutCountryCode.replace(/\D/g, '') 
      ? `${selectedCountryCode}${phoneWithoutCountryCode.replace(/\D/g, '')}`
      : ''

    if (validate) {
      const validationError = validate(fullPhoneNumber)
      if (validationError) {
        setError(validationError)
        return
      }
    }

    if (isExceedingLimit) {
      setError('Número de dígitos excede o limite para este país')
      return
    }

    setIsSaving(true)
    try {
      const success = await setFieldValue(fieldName, fullPhoneNumber)
      
      if (success) {
        setError(undefined)
        setIsEditing(false)
        setWasRecentlyEdited(true)
      } else {
        setError('Não foi possível salvar. Tente novamente ou cancele.')
      }
    } catch (error) {
      console.error('Erro ao salvar campo:', error)
      setError('Erro ao salvar. Tente novamente ou cancele.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCountryChange = (newCountryCode: string) => {
    setSelectedCountryCode(newCountryCode)
    if (error) setError(undefined)
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawInput = e.target.value

    // Remove caracteres não numéricos para validação de limite
    const digitsOnly = rawInput.replace(/\D/g, '')

    if (exceedsMaxDigits(selectedCountryCode, digitsOnly) && digitsOnly.length > phoneWithoutCountryCode.replace(/\D/g, '').length) {
      return // Previne digitação além do limite
    }

    // Formata apenas o número nacional (sem DDI)
    const formattedNational = formatNationalNumber(rawInput, selectedCountryCode)

    setPhoneWithoutCountryCode(formattedNational)
    if (error) setError(undefined)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleConfirm()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  const formatDisplayValue = () => {
    if (!currentValue || currentValue === emptyText) return emptyText
    
    try {
      return formatPhoneNumberBRWithDDI(currentValue)
    } catch (error) {
      console.error('Erro ao formatar telefone para exibição:', error)
      return currentValue
    }
  }
  
  const formattedDisplayValue = formatDisplayValue()
  
  const renderActionButtons = () => (
    <div className={cn(
      "absolute flex space-x-1",
      inline ? "right-1 top-1" : "right-1 top-1"
    )}>
      <Button 
        size="sm" 
        variant="ghost" 
        className={cn(
          "p-0 text-rose-500 hover:text-rose-600",
          inline ? "h-6 w-6" : "h-7 w-7"
        )}
        onClick={handleCancel}
        disabled={isSaving}
      >
        <X className={cn(inline ? "h-3 w-3" : "h-4 w-4")} />
      </Button>
      <Button 
        size="sm" 
        variant="ghost" 
        className={cn(
          "p-0 text-emerald-500 hover:text-emerald-600",
          inline ? "h-6 w-6" : "h-7 w-7"
        )}
        onClick={handleConfirm}
        disabled={isSaving}
      >
        {isSaving ? (
          <Loader2 className={cn("animate-spin", inline ? "h-3 w-3" : "h-4 w-4")} />
        ) : (
          <Check className={cn(inline ? "h-3 w-3" : "h-4 w-4")} />
        )}
      </Button>
    </div>
  )

  const recentlyEditedClass = wasRecentlyEdited 
    ? "bg-green-50 dark:bg-green-900/20 transition-colors duration-500" 
    : ""

  if (inline) {
    return (
      <div className={cn('group relative', className)}>
        {isEditing ? (
          <div className="relative">
            <div className="relative w-full">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <CountrySelect 
                  value={selectedCountryCode}
                  onValueChange={handleCountryChange}
                  disabled={isSaving}
                  showFlag={true}
                  triggerClassName="bg-transparent border-0 shadow-none hover:bg-transparent p-0"
                />
              </div>
              <Input 
                type="tel" 
                placeholder={placeholder}
                value={phoneWithoutCountryCode}
                onChange={handlePhoneChange}
                onKeyDown={handleKeyDown}
                disabled={isSaving}
                className={cn(
                  "pl-24 pr-20 h-8 text-sm min-w-[200px]",
                  (isExceedingLimit || error) ? 'border-red-500' : ''
                )}
                autoFocus
              />
            </div>
            {renderActionButtons()}
            
            {(error || isExceedingLimit) && (
              <p className="text-sm text-rose-500 mt-1">
                {error || 'Número de dígitos excede o limite para este país'}
              </p>
            )}
          </div>
        ) : (
          <div className={cn(
            "flex items-center gap-1 rounded transition-colors duration-200",
            recentlyEditedClass
          )}>
            <span className={cn(
              "text-sm text-slate-900 dark:text-slate-100 min-h-[20px] flex items-center",
              !formattedDisplayValue && "text-slate-400 dark:text-slate-500 italic",
              className
            )}>
              {formattedDisplayValue || emptyText}
            </span>
            
            {canEditThisField && (
              <Button
                size="sm"
                variant="ghost"
                className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-slate-100 dark:hover:bg-slate-700 rounded flex-shrink-0"
                onClick={handleEdit}
              >
                <Edit2 className="h-3 w-3 text-slate-400 hover:text-slate-600 dark:hover:text-slate-300" />
              </Button>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={cn(
      'group relative mb-2 pb-2 rounded-md',
      wasRecentlyEdited && 'bg-yellow-50/50 dark:bg-yellow-950/30',
      !currentValue && 'opacity-80',
      isEditing && 'bg-slate-50 dark:bg-slate-800/50 shadow-sm',
      className
    )}>
      <div className="flex justify-between items-center mb-1">
        <div className="flex items-center gap-2 text-sm text-slate-500 dark:text-slate-400">
          <span className="font-medium">{label}</span>
        </div>
        
        {canEditThisField && !isEditing && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={handleEdit}
          >
            <Edit2 className="h-3.5 w-3.5" />
            <span className="sr-only">Editar {label}</span>
          </Button>
        )}
      </div>
      
      {isEditing ? (
        <div className="mt-1 relative">
          <div className="relative w-full">
            <div className="absolute inset-y-0 left-0 flex items-center pl-3">
              <CountrySelect 
                value={selectedCountryCode}
                onValueChange={handleCountryChange}
                disabled={isSaving}
                showFlag={true}
                triggerClassName="bg-transparent border-0 shadow-none hover:bg-transparent p-0"
              />
            </div>
            <Input 
              type="tel" 
              placeholder={placeholder}
              value={phoneWithoutCountryCode}
              onChange={handlePhoneChange}
              onKeyDown={handleKeyDown}
              disabled={isSaving}
              className={cn(
                "pl-24 pr-20",
                (isExceedingLimit || error) ? 'border-red-500' : ''
              )}
              autoFocus
            />
          </div>
          {renderActionButtons()}
          
          {(error || isExceedingLimit) && (
            <p className="text-sm text-rose-500 mt-1">
              {error || 'Número de dígitos excede o limite para este país'}
            </p>
          )}
        </div>
      ) : (
        <div className="mt-1">
          <p className={cn(
            "text-base text-slate-900 dark:text-slate-100 rounded p-1",
            formattedDisplayValue === emptyText && "text-slate-400 dark:text-slate-500 italic",
            recentlyEditedClass
          )}>
            {formattedDisplayValue}
          </p>
        </div>
      )}
    </div>
  )
} 