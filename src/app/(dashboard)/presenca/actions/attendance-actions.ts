"use server";

import { revalidatePath } from "next/cache";
import { createClient } from "@/services/supabase/server";
import { getCurrentUser } from "@/services/auth/actions/auth-actions";
import {
  GenerateClassQRSchema,
  AttendanceFilterSchema,
  StudentAttendanceFilterSchema,
  AttendanceListFilterSchema,
  type GenerateClassQR,
  type AttendanceFilter,
  type StudentAttendanceFilter,
  type AttendanceListFilter,
  RemoveAttendanceSchema,
} from "../../aulas/actions/schemas/index";
import type { PaginatedResult, ClassWithDetails } from "../../aulas/types";

// Importar tipos e utilitários do arquivo dedicado
import type {
  AttendanceRecord,
  ClassAttendanceRecord,
  QRCodeData,
  TrendData,
  DailyStats,
  AttendanceStatsWithTrends,
  AttendanceStatsResponse
} from './attendance-utils';

import {
  calculateClassStatus,
  getClassStatus,
  isClassAvailableForCheckIn
} from './attendance-utils';





/**
 * Gera e armazena QR Code para uma aula
 */
export async function generateClassQR(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = GenerateClassQRSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;
    const supabase = await createClient();

    // Verificar se a aula existe e o usuário tem permissão
    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time, status, instructor_id")
      .eq("id", validatedData.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    // Verificar se o usuário é o instrutor da aula ou admin
    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para gerar QR Code desta aula" } };
    }

    if (!isClassAvailableForCheckIn(classData)) {
      return { success: false, errors: { class_id: "Aula não está disponível para check-in" } };
    }

    // Criar dados do QR Code
    const expiresAt = new Date();
    expiresAt.setMinutes(expiresAt.getMinutes() + validatedData.expires_in_minutes);

    const qrData: QRCodeData = {
      qr_code: `class_${validatedData.class_id}_${Date.now()}`,
      class_id: validatedData.class_id,
      expires_at: expiresAt.toISOString(),
      created_by: user.id,
    };

    // Codificar em base64
    const qrCode = Buffer.from(JSON.stringify(qrData)).toString('base64');

    // Desativar QR codes antigos desta aula
    await supabase
      .from("class_qr_codes")
      .update({ is_active: false })
      .eq("class_id", validatedData.class_id)
      .eq("tenant_id", tenantId)
      .eq("is_active", true);

    // Salvar novo QR code no banco de dados
    const { data: savedQRCode, error: saveError } = await supabase
      .from("class_qr_codes")
      .insert({
        tenant_id: tenantId,
        class_id: validatedData.class_id,
        qr_code: qrCode,
        raw_qr_data: qrData,
        expires_at: expiresAt.toISOString(),
        created_by: user.id,
        is_active: true
      })
      .select()
      .single();

    if (saveError) {
      console.error("Erro ao salvar QR Code:", saveError);
      return { success: false, errors: { _form: "Erro ao salvar QR Code no banco de dados" } };
    }

    revalidatePath("/aulas");

    return { 
      success: true, 
      data: {
        qr_code: qrCode,
        expires_at: qrData.expires_at,
        class_name: classData.name,
        class_id: classData.id,
        expires_in_minutes: validatedData.expires_in_minutes,
        stored_id: savedQRCode.id
      }
    };
  } catch (error) {
    console.error("Erro ao gerar QR Code:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Recupera QR code ativo de uma aula
 */
export async function getActiveClassQR(classId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se a aula existe e o usuário tem permissão
    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, start_time, end_time, status, instructor_id")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    // Verificar se o usuário é o instrutor da aula ou admin
    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para acessar QR Code desta aula" } };
    }

    // Buscar QR code ativo e não expirado
    const { data: qrCodeData, error: qrError } = await supabase
      .from("class_qr_codes")
      .select("*")
      .eq("class_id", classId)
      .eq("tenant_id", tenantId)
      .eq("is_active", true)
      .gt("expires_at", new Date().toISOString())
      .order("created_at", { ascending: false })
      .limit(1)
      .single();

    if (qrError && qrError.code !== 'PGRST116') {
      console.error("Erro ao buscar QR Code:", qrError);
      return { success: false, errors: { _form: "Erro ao buscar QR Code" } };
    }

    if (!qrCodeData) {
      return { 
        success: true, 
        data: null // Nenhum QR code válido encontrado
      };
    }

    return {
      success: true,
      data: {
        qr_code: qrCodeData.qr_code,
        expires_at: qrCodeData.expires_at,
        class_name: classData.name,
        class_id: classData.id,
        stored_id: qrCodeData.id,
        raw_data: qrCodeData.raw_qr_data
      }
    };
  } catch (error) {
    console.error("Erro ao recuperar QR Code:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Desativa um QR code específico
 */
export async function deactivateClassQR(qrCodeId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se o QR code existe e pertence ao tenant correto
    const { data: qrCodeData, error: qrError } = await supabase
      .from("class_qr_codes")
      .select(`
        id,
        class_id,
        classes (
          instructor_id
        )
      `)
      .eq("id", qrCodeId)
      .eq("tenant_id", tenantId)
      .single();

    if (qrError || !qrCodeData) {
      return { success: false, errors: { _form: "QR Code não encontrado" } };
    }

    // Verificar permissão
    const classData = qrCodeData.classes as any;
    if (user.app_metadata?.role !== "admin" && classData?.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para desativar este QR Code" } };
    }

    // Desativar o QR code
    const { error: updateError } = await supabase
      .from("class_qr_codes")
      .update({ is_active: false })
      .eq("id", qrCodeId)
      .eq("tenant_id", tenantId);

    if (updateError) {
      console.error("Erro ao desativar QR Code:", updateError);
      return { success: false, errors: { _form: "Erro ao desativar QR Code" } };
    }

    revalidatePath("/aulas");

    return { success: true };
  } catch (error) {
    console.error("Erro ao desativar QR Code:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Lista histórico de QR codes de uma aula
 */
export async function getClassQRHistory(classId: string) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Verificar se a aula existe e o usuário tem permissão
    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, instructor_id")
      .eq("id", classId)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    // Verificar se o usuário é o instrutor da aula ou admin
    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      return { success: false, errors: { _form: "Sem permissão para visualizar histórico desta aula" } };
    }

    // Buscar histórico de QR codes
    const { data: qrHistory, error: historyError } = await supabase
      .from("class_qr_codes")
      .select(`
        id,
        qr_code,
        expires_at,
        is_active,
        created_at,
        created_by_user:users!class_qr_codes_created_by_fkey (
          first_name,
          last_name,
          full_name
        )
      `)
      .eq("class_id", classId)
      .eq("tenant_id", tenantId)
      .order("created_at", { ascending: false });

    if (historyError) {
      console.error("Erro ao buscar histórico de QR Codes:", historyError);
      return { success: false, errors: { _form: "Erro ao buscar histórico de QR Codes" } };
    }

    return {
      success: true,
      data: {
        class_name: classData.name,
        qr_codes: qrHistory || []
      }
    };
  } catch (error) {
    console.error("Erro ao buscar histórico de QR Codes:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca lista de presença de uma aula
 */
export async function getAttendanceByClass(filters: unknown): Promise<{
  success: boolean;
  data?: PaginatedResult<ClassAttendanceRecord>;
  errors?: any;
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = AttendanceFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedFilters = validationResult.data;
    const supabase = await createClient();

    if (!validatedFilters.class_id) {
      return { success: false, errors: { class_id: "ID da aula é obrigatório" } };
    }

    // Verificar se a aula existe
    const { data: classData, error: classError } = await supabase
      .from("classes")
      .select("id, name, instructor_id, class_group_id")
      .eq("id", validatedFilters.class_id)
      .eq("tenant_id", tenantId)
      .single();

    if (classError || !classData) {
      return { success: false, errors: { class_id: "Aula não encontrada" } };
    }

    // Verificar permissão (admin, instrutor da aula ou instrutor responsável pela turma)
    if (user.app_metadata?.role !== "admin" && classData.instructor_id !== user.id) {
      // Se não é instrutor direto da aula, verificar se a aula pertence a uma turma que ele instrui
      if (classData.class_group_id) {
        const { data: groupData, error: groupError } = await supabase
          .from('class_groups')
          .select('instructor_id')
          .eq('id', classData.class_group_id)
          .single();

        if (groupError || !groupData || groupData.instructor_id !== user.id) {
          return { success: false, errors: { _form: "Sem permissão para visualizar esta lista de presença" } };
        }
      } else {
        return { success: false, errors: { _form: "Sem permissão para visualizar esta lista de presença" } };
      }
    }

    // Construir query base
    let query = supabase
      .from("attendance")
      .select(`
        id,
        student_id,
        checked_in_at,
        notes,
        student:students (
          id,
          check_in_code,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name,
            email,
            avatar_url
          ),
          current_belt_id
        )
      `, { count: 'exact' })
      .eq("tenant_id", tenantId)
      .eq("class_id", validatedFilters.class_id);

    // Aplicar filtros adicionais
    if (validatedFilters.student_id) {
      query = query.eq("student_id", validatedFilters.student_id);
    }

    if (validatedFilters.date_from) {
      query = query.gte("checked_in_at", validatedFilters.date_from);
    }

    if (validatedFilters.date_to) {
      query = query.lte("checked_in_at", validatedFilters.date_to);
    }

    if (validatedFilters.checked_in_by) {
      query = query.eq("checked_in_by", validatedFilters.checked_in_by);
    }

    // Aplicar ordenação
    query = query.order(validatedFilters.sort_by, { ascending: validatedFilters.sort_order === 'asc' });

    // Aplicar paginação
    const start = (validatedFilters.page - 1) * validatedFilters.limit;
    const end = start + validatedFilters.limit - 1;
    query = query.range(start, end);

    const { data: attendanceRecords, error, count } = await query;

    if (error) {
      console.error("Erro ao buscar lista de presença:", error);
      return { success: false, errors: { _form: "Erro ao buscar lista de presença" } };
    }

    const totalPages = Math.ceil((count || 0) / validatedFilters.limit);

    // Transformar os dados para o tipo esperado
    const transformedData: ClassAttendanceRecord[] = await Promise.all(
      (attendanceRecords || []).map(async (record) => {
        const studentData = record.student as any;
        const userData = studentData?.user as any;

        // Obter detalhes da faixa via RPC
        let beltData: any = null;
        if (studentData) {
          const { data: beltDetails } = await supabase.rpc(
            'get_student_current_belt_details',
            { student_id_param: studentData.id }
          );
          if (beltDetails && beltDetails.length > 0) {
            beltData = beltDetails[0];
          }
        }

        return {
          id: record.id,
          student_id: record.student_id,
          checked_in_at: record.checked_in_at,
          notes: record.notes,
          student: {
            id: studentData?.id || '',
            user: {
              first_name: userData?.first_name || '',
              last_name: userData?.last_name || null,
              full_name: userData?.full_name || null,
              email: userData?.email || '',
              avatar_url: userData?.avatar_url || '',
            },
            check_in_code: studentData?.check_in_code || null,
            current_belt: beltData ? {
              belt_color: beltData.belt_color,
              degree: beltData.degree,
              label: beltData.label,
              stripe_color: beltData.stripe_color,
              show_center_line: beltData.show_center_line,
              center_line_color: beltData.center_line_color,
            } : null,
          },
        };
      })
    );

    return {
      success: true,
      data: {
        data: transformedData,
        pagination: {
          page: validatedFilters.page,
          limit: validatedFilters.limit,
          total: count || 0,
          totalPages,
          hasNext: validatedFilters.page < totalPages,
          hasPrev: validatedFilters.page > 1,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar lista de presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca histórico de presença de um aluno
 */
export async function getAttendanceByStudent(filters: unknown): Promise<{
  success: boolean;
  data?: PaginatedResult<AttendanceRecord>;
  errors?: any;
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = StudentAttendanceFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedFilters = validationResult.data;
    const supabase = await createClient();

    // Verificar se o aluno existe
    const { data: student, error: studentError } = await supabase
      .from("students")
      .select("id, user_id")
      .eq("id", validatedFilters.student_id)
      .eq("tenant_id", tenantId)
      .single();

    if (studentError || !student) {
      return { success: false, errors: { student_id: "Aluno não encontrado" } };
    }

    // Verificar permissão (próprio aluno, admin, ou instrutor)
    const isOwnRecord = student.user_id === user.id;
    const isAdmin = user.app_metadata?.role === "admin";
    const isInstructor = user.app_metadata?.role === "instructor";

    if (!isOwnRecord && !isAdmin && !isInstructor) {
      return { success: false, errors: { _form: "Sem permissão para visualizar este histórico" } };
    }

    // Construir query base com inner join para permitir filtros na tabela de classes
    let queryBuilder = supabase
      .from("attendance")
      .select(`
        *,
        student:students (
          id,
          check_in_code,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name,
            avatar_url
          )
        ),
        class:classes!inner (
          id,
          name,
          start_time,
          end_time
        ),
        checked_in_by_user:users!attendance_checked_in_by_fkey (
          first_name,
          last_name,
          full_name
        )
      `, { count: 'exact' })
      .eq("tenant_id", tenantId)
      .eq("student_id", validatedFilters.student_id);

    // Aplicar filtros adicionais
    if (validatedFilters.date_from) {
      queryBuilder = queryBuilder.gte("checked_in_at", validatedFilters.date_from);
    }

    if (validatedFilters.date_to) {
      queryBuilder = queryBuilder.lte("checked_in_at", validatedFilters.date_to);
    }

    // Aplicar busca por nome da aula usando inner join
    if (validatedFilters.search) {
      // Com inner join, podemos filtrar diretamente na tabela relacionada
      queryBuilder = queryBuilder.ilike("class.name", `%${validatedFilters.search}%`);
    }

    let query = queryBuilder;

    // Aplicar ordenação
    query = query.order(validatedFilters.sort_by, { ascending: validatedFilters.sort_order === 'asc' });

    // Aplicar paginação
    const start = (validatedFilters.page - 1) * validatedFilters.limit;
    const end = start + validatedFilters.limit - 1;
    query = query.range(start, end);

    const { data: attendanceRecords, error, count } = await query;

    if (error) {
      console.error("Erro ao buscar histórico de presença:", error);
      return { success: false, errors: { _form: "Erro ao buscar histórico de presença" } };
    }

    const totalPages = Math.ceil((count || 0) / validatedFilters.limit);

    // Transformar os dados para o tipo esperado
    const transformedData: AttendanceRecord[] = (attendanceRecords || []).map(record => {
      const studentData = record.student as any;
      const userData = studentData?.user as any;
      const classData = record.class as any;
      const checkedInByData = record.checked_in_by_user as any;
      
      return {
        ...record,
        student: {
          id: studentData?.id || '',
          user: {
            first_name: userData?.first_name || '',
            last_name: userData?.last_name || null,
            full_name: userData?.full_name || null,
            avatar_url: userData?.avatar_url || null,
          },
          check_in_code: studentData?.check_in_code || null,
        },
        class: {
          id: classData?.id || '',
          name: classData?.name || '',
          start_time: classData?.start_time || '',
          end_time: classData?.end_time || '',
        },
        checked_in_by_user: {
          first_name: checkedInByData?.first_name || '',
          last_name: checkedInByData?.last_name || null,
          full_name: checkedInByData?.full_name || null,
        },
      };
    });

    return {
      success: true,
      data: {
        data: transformedData,
        pagination: {
          page: validatedFilters.page,
          limit: validatedFilters.limit,
          total: count || 0,
          totalPages,
          hasNext: validatedFilters.page < totalPages,
          hasPrev: validatedFilters.page > 1,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar histórico de presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Remove presença de um aluno de uma aula
 */
export async function removeAttendance(data: unknown) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    // Verificar permissões - apenas admin e instructor podem remover presença
    const userRole = user.app_metadata?.role;
    if (!['admin', 'instructor'].includes(userRole)) {
      return { success: false, errors: { _form: "Sem permissão para remover presença" } };
    }

    const validationResult = RemoveAttendanceSchema.safeParse(data);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const validatedData = validationResult.data;
    const supabase = await createClient();

    // Buscar o registro de presença para verificar se existe e obter informações
    const { data: attendanceRecord, error: attendanceError } = await supabase
      .from("attendance")
      .select(`
        id,
        student_id,
        class_id,
        checked_in_at,
        checked_in_by,
        notes,
        student:students (
          id,
          user:users!students_user_id_fkey (
            first_name,
            last_name,
            full_name
          )
        ),
        class:classes (
          id,
          name,
          start_time,
          end_time,
          status
        )
      `)
      .eq("id", validatedData.attendance_id)
      .eq("tenant_id", tenantId)
      .single();

    if (attendanceError || !attendanceRecord) {
      return { success: false, errors: { attendance_id: "Registro de presença não encontrado" } };
    }

    // Verificar se a aula ainda permite modificações (não pode estar finalizada há muito tempo)
    const classData = attendanceRecord.class as any;
    const classEndTime = new Date(classData.end_time);
    const now = new Date();
    const hoursAfterClass = (now.getTime() - classEndTime.getTime()) / (1000 * 60 * 60);

    // Permitir remoção apenas se a aula terminou há menos de 24 horas (configurável)
    if (hoursAfterClass > 24) {
      return { 
        success: false, 
        errors: { 
          _form: "Não é possível remover presença de aulas que terminaram há mais de 24 horas" 
        } 
      };
    }

    // Remover o registro de presença
    const { error: deleteError } = await supabase
      .from("attendance")
      .delete()
      .eq("id", validatedData.attendance_id)
      .eq("tenant_id", tenantId);

    if (deleteError) {
      console.error("Erro ao remover presença:", deleteError);
      return { success: false, errors: { _form: "Erro ao remover presença" } };
    }

    // Log da ação para auditoria (opcional - pode ser implementado futuramente)
    const studentData = attendanceRecord.student as any;
    const studentName = studentData?.user?.full_name || studentData?.user?.first_name || 'Aluno';
    
    console.log(`Presença removida: ${studentName} da aula ${classData.name} por ${user.email}. Motivo: ${validatedData.reason || 'Não informado'}`);

    revalidatePath("/aulas");
    
    return { 
      success: true, 
      data: {
        removed_attendance: {
          id: attendanceRecord.id,
          student_name: studentName,
          class_name: classData.name,
          checked_in_at: attendanceRecord.checked_in_at,
          removed_by: user.email,
          reason: validatedData.reason
        }
      }
    };
  } catch (error) {
    console.error("Erro ao remover presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca estatísticas gerais de presença (todas as aulas do tenant)
 */
export async function getAttendanceStats() {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Buscar todas as aulas do tenant (sem filtro de data para estatísticas gerais)
    const { data: fullClassData, error: fullClassError } = await supabase
      .from("classes")
      .select("id, start_time, end_time, status")
      .eq("tenant_id", tenantId);

    if (fullClassError) {
      console.error("Erro ao buscar dados completos das aulas:", fullClassError);
      return { success: false, errors: { _form: "Erro ao buscar estatísticas" } };
    }

    // Buscar todas as presenças do tenant (sem filtro de data para estatísticas gerais)
    const { data: attendanceStats, error: attendanceError } = await supabase
      .from("attendance")
      .select("id, student_id")
      .eq("tenant_id", tenantId);

    if (attendanceError) {
      console.error("Erro ao buscar estatísticas de presença:", attendanceError);
      return { success: false, errors: { _form: "Erro ao buscar estatísticas" } };
    }

    const fullClasses = fullClassData || [];
    const attendances = attendanceStats || [];

    // Usar status do banco de dados (atualizado pelo cron job) com fallback para cálculo
    const classesWithStatus = fullClasses.map(c => ({
      ...c,
      effectiveStatus: getClassStatus(c)
    }));

    const ongoingClasses = classesWithStatus.filter(c => c.effectiveStatus === 'ongoing');
    const upcomingClasses = classesWithStatus.filter(c => c.effectiveStatus === 'scheduled');
    const completedClasses = classesWithStatus.filter(c => c.effectiveStatus === 'completed');

    const stats = {
      totalClasses: fullClasses.length,
      ongoingClasses: ongoingClasses.length,
      upcomingClasses: upcomingClasses.length,
      completedClasses: completedClasses.length,
      totalAttendances: attendances.length,
      uniqueStudents: new Set(attendances.map(a => a.student_id)).size,
    };

    return { success: true, data: stats };
  } catch (error) {
    console.error("Erro ao buscar estatísticas:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca estatísticas de presença com dados históricos diários dos últimos 7 dias para gráficos
 */
export async function getAttendanceStatsWithTrends(): Promise<AttendanceStatsResponse> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const supabase = await createClient();

    // Data atual e período de 7 dias
    const today = new Date();
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(today.getDate() - 7);
    sevenDaysAgo.setHours(0, 0, 0, 0);
    
    const endOfToday = new Date(today);
    endOfToday.setHours(23, 59, 59, 999);

    const sevenDaysAhead = new Date(today);
    sevenDaysAhead.setDate(today.getDate() + 7);
    sevenDaysAhead.setHours(23, 59, 59, 999);

    // Buscar aulas dos últimos 7 dias com detalhes para calcular status e agrupamento por dia
    let classQuery = supabase
      .from("classes")
      .select("id, start_time, end_time, status, instructor_id")
      .eq("tenant_id", tenantId)
      .gte("start_time", sevenDaysAgo.toISOString())
      .lte("start_time", sevenDaysAhead.toISOString())
      .order("start_time", { ascending: true });

    // Aplicar filtros baseados no papel do usuário para instrutores
    if (user.app_metadata?.role === "instructor") {
      classQuery = classQuery.eq("instructor_id", user.id);
    }
    
    const { data: fullClassData, error: fullClassError } = await classQuery;

    if (fullClassError) {
      console.error("Erro ao buscar dados de aulas:", fullClassError);
      return { success: false, errors: { _form: "Erro ao buscar dados de aulas" } };
    }

    // Buscar registros de presença dos últimos 7 dias
    let attendanceQuery = supabase
      .from("attendance")
      .select("id, student_id, checked_in_at, class_id")
      .eq("tenant_id", tenantId)
      .gte("checked_in_at", sevenDaysAgo.toISOString())
      .lte("checked_in_at", sevenDaysAhead.toISOString())
      .order("checked_in_at", { ascending: true });

    // Aplicar filtros baseados no papel do usuário se for instrutor
    if (user.app_metadata?.role === "instructor" && fullClassData) {
      const instructorClassIds = fullClassData.map(c => c.id);
      if (instructorClassIds.length > 0) {
        attendanceQuery = attendanceQuery.in("class_id", instructorClassIds);
      } else {
        // Se o instrutor não tem aulas no período, retornar array vazio
        attendanceQuery = attendanceQuery.eq("class_id", "non-existent");
      }
    }
    
    const { data: attendanceData, error: attendanceError } = await attendanceQuery;

    if (attendanceError) {
      console.error("Erro ao buscar dados de presença:", attendanceError);
      return { success: false, errors: { _form: "Erro ao buscar dados de presença" } };
    }

    const classes = fullClassData || [];
    const attendances = attendanceData || [];

    // Processar classes com status efetivo
    const classesWithStatus = classes.map(c => ({
      ...c,
      effectiveStatus: getClassStatus(c)
    }));

    // Calcular estatísticas atuais baseadas no status efetivo
    const currentStats = {
      totalClasses: classes.length,
      ongoingClasses: classesWithStatus.filter(c => c.effectiveStatus === 'ongoing').length,
      upcomingClasses: classesWithStatus.filter(c => {
        if (c.effectiveStatus !== 'scheduled') return false;
        const classDate = new Date(c.start_time);
        return classDate >= today && classDate <= sevenDaysAhead;
      }).length,
      completedClasses: classesWithStatus.filter(c => c.effectiveStatus === 'completed').length,
      totalAttendances: attendances.length,
      uniqueStudents: new Set(attendances.map(a => a.student_id)).size,
    };

    // Gerar dados históricos diários dos últimos 7 dias
    const dailyData: DailyStats[] = [];
    const trendData = {
      total: [] as number[],
      ongoing: [] as number[],
      upcoming: [] as number[],
      completed: [] as number[]
    };

    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(today.getDate() - i);
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      // Filtrar aulas do dia
      const dayClasses = classes.filter(c => {
        const classDate = new Date(c.start_time);
        return classDate >= startOfDay && classDate <= endOfDay;
      });

      // Processar status das aulas do dia usando a lógica unificada
      const dayClassesWithStatus = dayClasses.map(c => ({
        ...c,
        effectiveStatus: getClassStatus(c)
      }));

      const dayStats = {
        date: date.toISOString().split('T')[0],
        total: dayClasses.length,
        ongoing: dayClassesWithStatus.filter(c => c.effectiveStatus === 'ongoing').length,
        upcoming: dayClassesWithStatus.filter(c => c.effectiveStatus === 'scheduled').length,
        completed: dayClassesWithStatus.filter(c => c.effectiveStatus === 'completed').length,
      };

      dailyData.push(dayStats);
      trendData.total.push(dayStats.total);
      trendData.ongoing.push(dayStats.ongoing);
      trendData.upcoming.push(dayStats.upcoming);
      trendData.completed.push(dayStats.completed);
    }

    // Recalcular dados de tendência para "upcoming" usando hoje -> +6 dias
    const upcomingFutureData: number[] = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      const startOfDay = new Date(date);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(date);
      endOfDay.setHours(23, 59, 59, 999);

      const dayUpcomingCount = classesWithStatus.filter(c => {
        if (c.effectiveStatus !== 'scheduled') return false;
        const classDate = new Date(c.start_time);
        return classDate >= startOfDay && classDate <= endOfDay;
      }).length;

      upcomingFutureData.push(dayUpcomingCount);
    }

    trendData.upcoming = upcomingFutureData;

    // Calcular tendências (variação percentual)
    const calculateTrend = (data: number[]): TrendData => {
      if (data.length < 2) return { value: 0, isPositive: true, data };
      
      const recent = data.slice(-3); // últimos 3 dias
      const previous = data.slice(-6, -3); // 3 dias anteriores
      
      if (recent.length === 0 || previous.length === 0) return { value: 0, isPositive: true, data };
      
      const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
      const previousAvg = previous.reduce((a, b) => a + b, 0) / previous.length;
      
      if (previousAvg === 0) {
        return { 
          value: recentAvg > 0 ? 100 : 0, 
          isPositive: recentAvg >= 0,
          data
        };
      }
      
      const percentChange = ((recentAvg - previousAvg) / previousAvg) * 100;
      return {
        value: Math.abs(Math.round(percentChange)),
        isPositive: percentChange >= 0,
        data
      };
    };

    const result: AttendanceStatsWithTrends = {
      ...currentStats,
      trends: {
        total: calculateTrend(trendData.total),
        ongoing: calculateTrend(trendData.ongoing),
        upcoming: calculateTrend(trendData.upcoming),
        completed: calculateTrend(trendData.completed)
      },
      dailyData,
    };

    return { success: true, data: result };
  } catch (error) {
    console.error("Erro ao buscar estatísticas com tendências:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}

/**
 * Busca aulas com informações de presença para a listagem da página de presença
 * Ordenadas da mais recente para mais antiga por padrão
 */
export async function getClassesWithAttendance(filters: unknown): Promise<{ 
  success: boolean; 
  data?: PaginatedResult<ClassWithDetails>; 
  errors?: any; 
}> {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return { success: false, errors: { _form: "Usuário não autenticado" } };
    }

    const tenantId = user.app_metadata?.tenant_id;
    if (!tenantId) {
      return { success: false, errors: { _form: "Tenant não identificado" } };
    }

    const validationResult = AttendanceListFilterSchema.safeParse(filters);
    if (!validationResult.success) {
      return { success: false, errors: validationResult.error.format() };
    }

    const filterData = validationResult.data;
    const supabase = await createClient();

    // Verificar permissões baseadas no papel do usuário
    let query = supabase
      .from("classes")
      .select(`
        *,
        instructor:users!classes_instructor_id_fkey(
          id,
          first_name,
          last_name,
          full_name,
          avatar_url
        ),
        branch:branches!classes_branch_id_fkey(
          id,
          name
        ),
        class_group:class_groups!classes_class_group_id_fkey(
          id,
          name,
          category
        ),
        attendance(count)
      `)
      .eq("tenant_id", tenantId);

    // Aplicar filtros baseados no papel do usuário
    if (user.app_metadata?.role === "instructor") {
      query = query.eq("instructor_id", user.id);
    }

    // Aplicar filtros de busca
    let matchingInstructorIds: string[] = [];
    
    if (filterData.search) {
      const { data: matchingInstructors } = await supabase
        .from("users")
        .select("id")
        .eq("tenant_id", tenantId)
        .or(`first_name.ilike.%${filterData.search}%,last_name.ilike.%${filterData.search}%,full_name.ilike.%${filterData.search}%`);

      matchingInstructorIds = matchingInstructors?.map(i => i.id) || [];
    }

    if (filterData.search) {
      if (matchingInstructorIds.length > 0) {
        query = query.or(`name.ilike.%${filterData.search}%,description.ilike.%${filterData.search}%,instructor_id.in.(${matchingInstructorIds.join(',')})`);
      } else {
        query = query.or(`name.ilike.%${filterData.search}%,description.ilike.%${filterData.search}%`);
      }
    }

    // Aplicar filtros específicos
    if (filterData.instructor_id) {
      query = query.eq("instructor_id", filterData.instructor_id);
    }

    if (filterData.branch_id) {
      query = query.eq("branch_id", filterData.branch_id);
    }

    if (filterData.class_group_id !== undefined) {
      if (filterData.class_group_id === null) {
        query = query.is("class_group_id", null);
      } else {
        query = query.eq("class_group_id", filterData.class_group_id);
      }
    }

    if (filterData.status) {
      // Note: O status é calculado dinamicamente, então filtraremos após buscar os dados
      // Para otimização, podemos aplicar alguns filtros básicos aqui
      if (filterData.status === 'completed') {
        query = query.in("status", ["scheduled", "ongoing", "completed"]);
      } else if (filterData.status === 'ongoing') {
        query = query.in("status", ["scheduled", "ongoing"]);
      } else if (filterData.status === 'scheduled') {
        query = query.eq("status", "scheduled");
      } else {
        query = query.eq("status", filterData.status);
      }
    }

    if (filterData.date_from) {
      query = query.gte("start_time", filterData.date_from);
    }

    if (filterData.date_to) {
      query = query.lte("start_time", filterData.date_to);
    }

    // Criar query de contagem para paginação
    let countQuery = supabase
      .from("classes")
      .select("*", { count: "exact", head: true })
      .eq("tenant_id", tenantId);

    // Aplicar os mesmos filtros na query de contagem
    if (user.app_metadata?.role === "instructor") {
      countQuery = countQuery.eq("instructor_id", user.id);
    }

    if (filterData.search) {
      if (matchingInstructorIds.length > 0) {
        countQuery = countQuery.or(`name.ilike.%${filterData.search}%,description.ilike.%${filterData.search}%,instructor_id.in.(${matchingInstructorIds.join(',')})`);
      } else {
        countQuery = countQuery.or(`name.ilike.%${filterData.search}%,description.ilike.%${filterData.search}%`);
      }
    }

    if (filterData.instructor_id) {
      countQuery = countQuery.eq("instructor_id", filterData.instructor_id);
    }

    if (filterData.branch_id) {
      countQuery = countQuery.eq("branch_id", filterData.branch_id);
    }

    if (filterData.class_group_id !== undefined) {
      if (filterData.class_group_id === null) {
        countQuery = countQuery.is("class_group_id", null);
      } else {
        countQuery = countQuery.eq("class_group_id", filterData.class_group_id);
      }
    }

    if (filterData.status) {
      if (filterData.status === 'completed') {
        countQuery = countQuery.in("status", ["scheduled", "ongoing", "completed"]);
      } else if (filterData.status === 'ongoing') {
        countQuery = countQuery.in("status", ["scheduled", "ongoing"]);
      } else if (filterData.status === 'scheduled') {
        countQuery = countQuery.eq("status", "scheduled");
      } else {
        countQuery = countQuery.eq("status", filterData.status);
      }
    }

    if (filterData.date_from) {
      countQuery = countQuery.gte("start_time", filterData.date_from);
    }

    if (filterData.date_to) {
      countQuery = countQuery.lte("start_time", filterData.date_to);
    }

    // Executar query de contagem
    const { count, error: countError } = await countQuery;

    if (countError) {
      console.error("Erro ao contar aulas:", countError);
      return { success: false, errors: { _form: "Erro ao buscar aulas" } };
    }

    // Aplicar ordenação (mais recente primeiro)
    query = query.order("start_time", { ascending: false });

    // Aplicar paginação
    const start = (filterData.page - 1) * filterData.pageSize;
    const end = start + filterData.pageSize - 1;
    query = query.range(start, end);

    // Executar query principal
    const { data: classes, error } = await query;

    if (error) {
      console.error("Erro ao buscar aulas:", error);
      return { success: false, errors: { _form: "Erro ao buscar aulas" } };
    }

    // Transformar dados e calcular status efetivo
    const transformedClasses = (classes || []).map(classItem => {
      const effectiveStatus = getClassStatus(classItem);
      
      return {
        ...classItem,
        status: effectiveStatus,
        _count: {
          attendance: classItem.attendance?.[0]?.count || 0
        },
        attendance: undefined // Remove array de attendance da resposta
      };
    }).filter(classItem => {
      // Filtrar pelo status efetivo se especificado
      if (filterData.status) {
        return classItem.status === filterData.status;
      }
      return true;
    });

    // Calcular total real se filtro de status foi aplicado
    let actualTotal = count || 0;
    if (filterData.status) {
      // Se estamos filtrando por status, precisamos contar apenas os itens filtrados
      actualTotal = transformedClasses.length;
    }

    // Calcular informações de paginação
    const totalPages = Math.ceil(actualTotal / filterData.pageSize);
    const hasNext = filterData.page < totalPages;
    const hasPrev = filterData.page > 1;

    return {
      success: true,
      data: {
        data: transformedClasses as ClassWithDetails[],
        pagination: {
          page: filterData.page,
          limit: filterData.pageSize,
          total: actualTotal,
          totalPages,
          hasNext,
          hasPrev,
        },
      },
    };
  } catch (error) {
    console.error("Erro ao buscar aulas com presença:", error);
    return { success: false, errors: { _form: "Erro interno do servidor" } };
  }
}