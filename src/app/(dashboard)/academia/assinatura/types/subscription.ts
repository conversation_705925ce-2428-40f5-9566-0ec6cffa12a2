export type SubscriptionStatus = 'active' | 'pending' | 'overdue' | 'canceled';

export type PaymentMethod = 'pix' | 'credit_card' | 'debit_card' | 'bank_transfer';

export interface SubscriptionData {
  planName: string;
  status: SubscriptionStatus;
  amount: number;
  dueDate: string;
  paymentMethod: PaymentMethod;
  lastPayment: string;
  nextBilling: string;
  features: string[];
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  features: string[];
  maxStudents: number;
  isPopular?: boolean;
}

export interface PaymentHistory {
  id: string;
  amount: number;
  date: string;
  status: 'paid' | 'pending' | 'failed';
  paymentMethod: PaymentMethod;
  description: string;
}
