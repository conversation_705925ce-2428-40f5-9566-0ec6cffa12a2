"use client"

import React, { useState } from 'react';
import { usePageTitle } from '@/contexts/PageTitleContext';
import { Button } from '@/components/ui/button';
import { Settings } from 'lucide-react';
import {
  SubscriptionStatusCard,
  PaymentMethodCard,
  NextPaymentCard,
  PlanFeaturesCard,
  SubscriptionActionsCard,
  ChangePaymentMethodModal,
  PaymentHistoryModal
} from './components';
import { SubscriptionData, PaymentMethod } from './types/subscription';

export default function AssinaturaPage() {
  // Configurar título da página
  usePageTitle('Assinatura');

  // Estados para os modais
  const [showPaymentMethodModal, setShowPaymentMethodModal] = useState(false);
  const [showHistoryModal, setShowHistoryModal] = useState(false);

  // Mock data - será substituído por dados reais no futuro
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData>({
    planName: 'Plano Premium',
    status: 'active', // active, pending, overdue, canceled
    amount: 199.90,
    dueDate: '2024-08-15',
    paymentMethod: 'pix',
    lastPayment: '2024-07-15',
    nextBilling: '2024-08-15',
    features: [
      'Até 500 alunos',
      'Relatórios avançados',
      'Suporte prioritário',
      'Backup automático',
      'Integração com apps'
    ]
  });

  // Handlers para as ações
  const handleChangePaymentMethod = () => {
    setShowPaymentMethodModal(true);
  };

  const handleSavePaymentMethod = (method: PaymentMethod) => {
    setSubscriptionData(prev => ({
      ...prev,
      paymentMethod: method
    }));
    console.log('Método de pagamento alterado para:', method);
  };

  const handlePayNow = () => {
    console.log('Pagar agora');
    // Aqui será implementada a lógica de pagamento
  };

  const handleChangePlan = () => {
    console.log('Alterar plano');
    // Aqui será implementada a lógica para alterar plano
  };

  const handleViewHistory = () => {
    setShowHistoryModal(true);
  };

  const handleDownloadReceipts = () => {
    console.log('Baixar comprovantes');
    // Aqui será implementada a lógica para baixar comprovantes
  };

  const handleCancelSubscription = () => {
    console.log('Cancelar assinatura');
    // Aqui será implementada a lógica para cancelar assinatura
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Assinatura</h1>
          <p className="text-muted-foreground">
            Gerencie sua assinatura do ApexDojo
          </p>
        </div>
        <Button variant="outline" className="gap-2">
          <Settings className="h-4 w-4" />
          Configurações
        </Button>
      </div>

      {/* Status da Assinatura */}
      <SubscriptionStatusCard
        planName={subscriptionData.planName}
        status={subscriptionData.status}
        amount={subscriptionData.amount}
        nextBilling={subscriptionData.nextBilling}
      />

      <div className="grid gap-6 md:grid-cols-2">
        {/* Informações de Pagamento */}
        <PaymentMethodCard
          paymentMethod={subscriptionData.paymentMethod}
          onChangePaymentMethod={handleChangePaymentMethod}
        />

        {/* Próximo Pagamento */}
        <NextPaymentCard
          amount={subscriptionData.amount}
          dueDate={subscriptionData.dueDate}
          lastPayment={subscriptionData.lastPayment}
          onPayNow={handlePayNow}
        />
      </div>

      {/* Recursos do Plano */}
      <PlanFeaturesCard features={subscriptionData.features} />

      {/* Ações */}
      <SubscriptionActionsCard
        onChangePlan={handleChangePlan}
        onViewHistory={handleViewHistory}
        onDownloadReceipts={handleDownloadReceipts}
        onCancelSubscription={handleCancelSubscription}
      />
    </div>
  );
}
