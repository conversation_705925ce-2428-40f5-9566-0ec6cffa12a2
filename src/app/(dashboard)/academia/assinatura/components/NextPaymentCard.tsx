"use client"

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Calendar, DollarSign } from 'lucide-react';

interface NextPaymentCardProps {
  amount: number;
  dueDate: string;
  lastPayment: string;
  onPayNow?: () => void;
}

export function NextPaymentCard({
  amount,
  dueDate,
  lastPayment,
  onPayNow
}: NextPaymentCardProps) {
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getDaysUntilDue = (dueDateString: string) => {
    const today = new Date();
    const due = new Date(dueDateString);
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const daysUntilDue = getDaysUntilDue(dueDate);
  const isOverdue = daysUntilDue < 0;
  const isDueSoon = daysUntilDue <= 7 && daysUntilDue >= 0;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Próximo Pagamento
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-muted-foreground">Valor:</span>
            <span className="font-medium">
              {formatCurrency(amount)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Vencimento:</span>
            <span className={`font-medium ${
              isOverdue 
                ? 'text-red-600' 
                : isDueSoon 
                  ? 'text-yellow-600' 
                  : 'text-foreground'
            }`}>
              {formatDate(dueDate)}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">Último pagamento:</span>
            <span className="font-medium">
              {formatDate(lastPayment)}
            </span>
          </div>
        </div>

        {/* Status do vencimento */}
        {isOverdue && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-700 dark:text-red-300 font-medium">
              Pagamento em atraso há {Math.abs(daysUntilDue)} dias
            </p>
          </div>
        )}

        {isDueSoon && !isOverdue && (
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <p className="text-sm text-yellow-700 dark:text-yellow-300 font-medium">
              Vence em {daysUntilDue} {daysUntilDue === 1 ? 'dia' : 'dias'}
            </p>
          </div>
        )}

        <Button 
          className="w-full gap-2"
          variant={isOverdue ? "destructive" : "default"}
          onClick={onPayNow}
        >
          <DollarSign className="h-4 w-4" />
          {isOverdue ? 'Pagar em Atraso' : 'Pagar Agora'}
        </Button>
      </CardContent>
    </Card>
  );
}
