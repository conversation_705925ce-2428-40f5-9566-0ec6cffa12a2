"use client"

import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { CheckCircle, Clock, XCircle, Download } from 'lucide-react';
import { FaPix } from 'react-icons/fa6';
import { CreditCard } from 'lucide-react';
import { PaymentHistory, PaymentMethod } from '../types/subscription';
import { Button } from '@/components/ui/button';

interface PaymentHistoryModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function PaymentHistoryModal({
  isOpen,
  onClose
}: PaymentHistoryModalProps) {
  // Mock data - será substituído por dados reais no futuro
  const paymentHistory: PaymentHistory[] = [
    {
      id: '1',
      amount: 199.90,
      date: '2024-07-15',
      status: 'paid',
      paymentMethod: 'pix',
      description: 'Mensalidade Julho 2024'
    },
    {
      id: '2',
      amount: 199.90,
      date: '2024-06-15',
      status: 'paid',
      paymentMethod: 'pix',
      description: 'Mensalidade Junho 2024'
    },
    {
      id: '3',
      amount: 199.90,
      date: '2024-05-15',
      status: 'paid',
      paymentMethod: 'credit_card',
      description: 'Mensalidade Maio 2024'
    },
    {
      id: '4',
      amount: 199.90,
      date: '2024-04-15',
      status: 'failed',
      paymentMethod: 'credit_card',
      description: 'Mensalidade Abril 2024'
    }
  ];

  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'paid':
        return {
          label: 'Pago',
          variant: 'statusActive' as const,
          icon: CheckCircle,
          color: 'text-green-600'
        };
      case 'pending':
        return {
          label: 'Pendente',
          variant: 'outline' as const,
          icon: Clock,
          color: 'text-yellow-600'
        };
      case 'failed':
        return {
          label: 'Falhou',
          variant: 'destructive' as const,
          icon: XCircle,
          color: 'text-red-600'
        };
      default:
        return {
          label: 'Desconhecido',
          variant: 'outline' as const,
          icon: Clock,
          color: 'text-gray-600'
        };
    }
  };

  const getPaymentMethodConfig = (method: PaymentMethod) => {
    switch (method) {
      case 'pix':
        return {
          name: 'PIX',
          icon: FaPix,
          color: 'text-green-600'
        };
      case 'credit_card':
        return {
          name: 'Cartão de Crédito',
          icon: CreditCard,
          color: 'text-blue-600'
        };
      case 'debit_card':
        return {
          name: 'Cartão de Débito',
          icon: CreditCard,
          color: 'text-purple-600'
        };
      case 'bank_transfer':
        return {
          name: 'Transferência Bancária',
          icon: CreditCard,
          color: 'text-indigo-600'
        };
      default:
        return {
          name: 'Não definido',
          icon: CreditCard,
          color: 'text-gray-600'
        };
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Histórico de Pagamentos</DialogTitle>
          <DialogDescription>
            Visualize todos os pagamentos da sua assinatura.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {paymentHistory.map((payment) => {
            const statusConfig = getStatusConfig(payment.status);
            const paymentConfig = getPaymentMethodConfig(payment.paymentMethod);
            const StatusIcon = statusConfig.icon;
            const PaymentIcon = paymentConfig.icon;

            return (
              <Card key={payment.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <StatusIcon className={`h-4 w-4 ${statusConfig.color}`} />
                        <div>
                          <p className="font-medium">{payment.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <PaymentIcon className={`h-3 w-3 ${paymentConfig.color}`} />
                            <span className="text-xs text-muted-foreground">
                              {paymentConfig.name}
                            </span>
                            <span className="text-xs text-muted-foreground">
                              • {formatDate(payment.date)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <p className="font-medium">
                          {formatCurrency(payment.amount)}
                        </p>
                        <Badge variant={statusConfig.variant} className="text-xs">
                          {statusConfig.label}
                        </Badge>
                      </div>
                      {payment.status === 'paid' && (
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {paymentHistory.length === 0 && (
          <div className="text-center py-8">
            <p className="text-muted-foreground">
              Nenhum pagamento encontrado.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
