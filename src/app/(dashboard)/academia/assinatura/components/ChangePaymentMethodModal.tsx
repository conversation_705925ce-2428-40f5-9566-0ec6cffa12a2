"use client"

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CreditCard, Check } from 'lucide-react';
import { FaPix } from 'react-icons/fa6';
import { PaymentMethod } from '../types/subscription';

interface ChangePaymentMethodModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentMethod: PaymentMethod;
  onSave: (method: PaymentMethod) => void;
}

export function ChangePaymentMethodModal({
  isOpen,
  onClose,
  currentMethod,
  onSave
}: ChangePaymentMethodModalProps) {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>(currentMethod);

  const paymentMethods = [
    {
      id: 'pix' as PaymentMethod,
      name: 'P<PERSON>',
      description: 'Transferência instantânea',
      icon: FaPix,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/30'
    },
    {
      id: 'credit_card' as PaymentMethod,
      name: 'Cartão de Crédito',
      description: 'Visa, Mastercard, Elo',
      icon: CreditCard,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/30'
    },
    {
      id: 'debit_card' as PaymentMethod,
      name: 'Cartão de Débito',
      description: 'Débito automático',
      icon: CreditCard,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/30'
    },
    {
      id: 'bank_transfer' as PaymentMethod,
      name: 'Transferência Bancária',
      description: 'TED/DOC',
      icon: CreditCard,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100 dark:bg-indigo-900/30'
    }
  ];

  const handleSave = () => {
    onSave(selectedMethod);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Alterar Método de Pagamento</DialogTitle>
          <DialogDescription>
            Selecione o novo método de pagamento para sua assinatura.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-3">
          {paymentMethods.map((method) => {
            const Icon = method.icon;
            const isSelected = selectedMethod === method.id;

            return (
              <Card
                key={method.id}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  isSelected 
                    ? 'ring-2 ring-primary border-primary' 
                    : 'hover:border-gray-300'
                }`}
                onClick={() => setSelectedMethod(method.id)}
              >
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 rounded-lg ${method.bgColor}`}>
                        <Icon className={`h-5 w-5 ${method.color}`} />
                      </div>
                      <div>
                        <p className="font-medium">{method.name}</p>
                        <p className="text-sm text-muted-foreground">
                          {method.description}
                        </p>
                      </div>
                    </div>
                    {isSelected && (
                      <Check className="h-5 w-5 text-primary" />
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleSave}>
            Salvar Alterações
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
