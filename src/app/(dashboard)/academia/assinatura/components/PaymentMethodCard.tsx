"use client"

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CreditCard } from 'lucide-react';
import { FaPix } from 'react-icons/fa6';

interface PaymentMethodCardProps {
  paymentMethod: string;
  onChangePaymentMethod?: () => void;
}

export function PaymentMethodCard({
  paymentMethod,
  onChangePaymentMethod
}: PaymentMethodCardProps) {
  const getPaymentMethodConfig = (method: string) => {
    switch (method) {
      case 'pix':
        return {
          name: 'PIX',
          icon: FaPix,
          color: 'text-green-600'
        };
      case 'credit_card':
        return {
          name: 'Cartão de Crédito',
          icon: CreditCard,
          color: 'text-blue-600'
        };
      case 'debit_card':
        return {
          name: 'Cartão de Débito',
          icon: CreditCard,
          color: 'text-purple-600'
        };
      case 'bank_transfer':
        return {
          name: 'Transferência Bancária',
          icon: CreditCard,
          color: 'text-indigo-600'
        };
      default:
        return {
          name: 'Não definido',
          icon: CreditCard,
          color: 'text-gray-600'
        };
    }
  };

  const paymentConfig = getPaymentMethodConfig(paymentMethod);
  const PaymentIcon = paymentConfig.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CreditCard className="h-5 w-5" />
          Método de Pagamento
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800">
            <PaymentIcon className={`h-5 w-5 ${paymentConfig.color}`} />
          </div>
          <div>
            <p className="font-medium">{paymentConfig.name}</p>
            <p className="text-sm text-muted-foreground">
              Método principal
            </p>
          </div>
        </div>
        <Button 
          variant="outline" 
          className="w-full"
          onClick={onChangePaymentMethod}
        >
          Alterar Método de Pagamento
        </Button>
      </CardContent>
    </Card>
  );
}
