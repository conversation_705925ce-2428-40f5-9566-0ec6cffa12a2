"use client"

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle,
  Clock,
  AlertCircle,
  Zap
} from 'lucide-react';

interface SubscriptionStatusCardProps {
  planName: string;
  status: 'active' | 'pending' | 'overdue' | 'canceled';
  amount: number;
  nextBilling: string;
}

export function SubscriptionStatusCard({
  planName,
  status,
  amount,
  nextBilling
}: SubscriptionStatusCardProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'active':
        return {
          label: 'Ativa',
          variant: 'statusActive' as const,
          icon: CheckCircle,
          color: 'text-green-600'
        };
      case 'pending':
        return {
          label: 'Pendente',
          variant: 'outline' as const,
          icon: Clock,
          color: 'text-yellow-600'
        };
      case 'overdue':
        return {
          label: 'Em Atraso',
          variant: 'destructive' as const,
          icon: AlertCircle,
          color: 'text-red-600'
        };
      case 'canceled':
        return {
          label: '<PERSON><PERSON>ada',
          variant: 'secondary' as const,
          icon: AlertCircle,
          color: 'text-gray-600'
        };
      default:
        return {
          label: 'Desconhecido',
          variant: 'outline' as const,
          icon: AlertCircle,
          color: 'text-gray-600'
        };
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const statusConfig = getStatusConfig(status);
  const StatusIcon = statusConfig.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5 text-primary" />
          Status da Assinatura
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <StatusIcon className={`h-5 w-5 ${statusConfig.color}`} />
              <span className="text-lg font-semibold">{planName}</span>
              <Badge variant={statusConfig.variant}>
                {statusConfig.label}
              </Badge>
            </div>
            <p className="text-muted-foreground">
              Próximo vencimento: {formatDate(nextBilling)}
            </p>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">
              {formatCurrency(amount)}
            </div>
            <p className="text-sm text-muted-foreground">por mês</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
