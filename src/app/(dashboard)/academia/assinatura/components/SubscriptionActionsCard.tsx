"use client"

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface SubscriptionActionsCardProps {
  onChangePlan?: () => void;
  onViewHistory?: () => void;
  onDownloadReceipts?: () => void;
  onCancelSubscription?: () => void;
}

export function SubscriptionActionsCard({
  onChangePlan,
  onViewHistory,
  onDownloadReceipts,
  onCancelSubscription
}: SubscriptionActionsCardProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Gerenciar Assinatura</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap gap-3">
          <Button 
            variant="outline"
            onClick={onChangePlan}
          >
            Alterar Plano
          </Button>
          <Button 
            variant="outline"
            onClick={onViewHistory}
          >
            Histórico de Pagamentos
          </Button>
          <Button 
            variant="outline"
            onClick={onDownloadReceipts}
          >
            Baixar Comprovantes
          </Button>
          <Button 
            variant="destructive" 
            className="ml-auto"
            onClick={onCancelSubscription}
          >
            Cancelar Assinatura
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
