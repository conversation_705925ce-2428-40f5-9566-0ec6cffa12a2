'use client';

import { DotLottieReact } from '@lottiefiles/dotlottie-react';

interface LottieAnimationProps {
  animationPath: string;
  className?: string;
  loop?: boolean;
  autoplay?: boolean;
  speed?: number;
}

export function LottieAnimation({ 
  animationPath, 
  className = '', 
  loop = true, 
  autoplay = true,
  speed = 1 
}: LottieAnimationProps) {
  return (
    <DotLottieReact
      src={animationPath}
      loop={loop}
      autoplay={autoplay}
      speed={speed}
      className={className}
    />
  );
}