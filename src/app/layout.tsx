import type { Metadata, Viewport } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from './providers'
import { Toaster } from "@/components/ui/sonner";
import { createAdminClient } from '@/services/supabase/server';
import { TenantExtractorServer } from '@/services/tenant/tenant-extractor-server';
import { headers, cookies } from 'next/headers';
import ChatwootWidget from '@/components/shared/ChatwootWidget';

const inter = Inter({ subsets: ['latin'] })

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
}

export const dynamic = 'force-dynamic';

export async function generateMetadata(): Promise<Metadata> {
  const supabase = await createAdminClient();
  
  const headersList = await headers();
  const cookieStore = await cookies();
  const host = headersList.get('host') || '';

  const extractor = new TenantExtractorServer();
  const tenantSlug = await extractor.extractTenantSlug(headersList, cookieStore);
  
  const { data: tenant } = await supabase
    .from('tenants')
    .select('name, description, logo_url')
    .eq('slug', tenantSlug)
    .single();
  
  const protocol = process.env.NODE_ENV === 'development' ? 'http' : 'https';
  
  const buildVersion = process.env.NEXT_PUBLIC_BUILD_VERSION || '1';
  const tenantVersion = (tenant as any)?.updated_at ? new Date((tenant as any).updated_at).getTime() : 0;
  const faviconUrl = `${protocol}://${host}/api/favicon?tenant=${tenantSlug}&v=${buildVersion}-${tenantVersion}`;
  
  return {
    title: tenant?.name || 'Academia de Jiu-Jitsu',
    description: tenant?.description || 'Sistema de gerenciamento de academia de Jiu-Jitsu',
    icons: {
      icon: [
        { url: faviconUrl, type: 'image/png', sizes: '32x32' },
        { url: faviconUrl, type: 'image/png', sizes: '16x16' }
      ],
      shortcut: faviconUrl,
      apple: faviconUrl
    },
    // Removendo headers que impedem o cache
    other: {
      'preload': faviconUrl
    }
  };
}

// Função otimizada para obter cores do tenant
async function getTenantColorsOptimized(): Promise<{
  primaryColor: string | null,
  secondaryColor: string | null,
  logoUrl: string | null,
  tenantName: string | null
}> {
  try {
    const extractor = new TenantExtractorServer();
    const slug = await extractor.extractTenantSlug();
    
    if (!slug) {
      return {
        primaryColor: null,
        secondaryColor: null,
        logoUrl: null,
        tenantName: null
      };
    }

    const supabase = await createAdminClient();
    const { data: tenant } = await supabase
      .from('tenants')
      .select('name, primary_color, secondary_color, logo_url')
      .eq('slug', slug)
      .single();
    
    return {
      primaryColor: tenant?.primary_color || null,
      secondaryColor: tenant?.secondary_color || null,
      logoUrl: tenant?.logo_url || null,
      tenantName: tenant?.name || null
    };
  } catch (error) {
    console.error('Erro ao obter informações do tenant:', error);
    return {
      primaryColor: null,
      secondaryColor: null,
      logoUrl: null,
      tenantName: null
    };
  }
}

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { primaryColor, secondaryColor, logoUrl, tenantName } = await getTenantColorsOptimized();
  
  // Obter o slug correto do tenant para o favicon
  const extractor = new TenantExtractorServer();
  const tenantSlug = await extractor.extractTenantSlug();
  
  // Buscar updated_at para incluir no versionamento do favicon e forçar revalidação
  const supabase = await createAdminClient();
  const { data: tenantData } = await supabase
    .from('tenants')
    .select('updated_at')
    .eq('slug', tenantSlug)
    .single();

  const tenantVersion = tenantData?.updated_at ? new Date(tenantData.updated_at as unknown as string).getTime() : 0;
  
  return (
    <html lang="pt-BR" suppressHydrationWarning>
      <head>
        {/* Usamos a mesma versão estática definida em generateMetadata para evitar divergências entre server e client */}
        <link 
          rel="icon"
          href={`/api/favicon?tenant=${tenantSlug}&v=${process.env.NEXT_PUBLIC_BUILD_VERSION || '1'}-${tenantVersion}`}
          type="image/png"
          crossOrigin="anonymous"
        />
      </head>
      <body suppressHydrationWarning className={`${inter.className} min-h-screen bg-background text-foreground antialiased`}>
        <Providers 
          primaryColor={primaryColor} 
          secondaryColor={secondaryColor} 
          logoUrl={logoUrl}
          tenantName={tenantName}
        >
          {children}
        </Providers>
        <Toaster />
        <ChatwootWidget />
      </body>
    </html>
  )
}
