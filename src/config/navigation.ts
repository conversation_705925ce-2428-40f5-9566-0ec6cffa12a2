interface NavigationConfig {
  mainNav: MainNavItem[];
  sidebarNav: SidebarNavItem[];
  routes: Routes;
}

interface Routes {
  public: string[];
  protected: string[];
  auth: string[];
  bypass: string[];
  isPublic: (path: string) => boolean;
  isProtected: (path: string) => boolean;
  isAuth: (path: string) => boolean;
  shouldBypass: (path: string) => boolean;
}

interface MainNavItem {
  title: string;
  href: string;
  disabled?: boolean;
  external?: boolean;
  icon?: string;
}

interface SidebarNavItem {
  title: string;
  href?: string;
  disabled?: boolean;
  external?: boolean;
  icon?: string;
  adminOnly?: boolean;
  instructorOnly?: boolean;
  instructorAccess?: boolean;
  studentAccess?: boolean;
  badge?: number;
  items?: SidebarNavItem[];
  isNew?: boolean;
}

export const navigationConfig: NavigationConfig = {
  mainNav: [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: "dashboard",
    },
    {
      title: "Perfil",
      href: "/perfil",
      icon: "user",
    },
  ],
  
  sidebarNav: [
    {
      title: "Principal",
      icon: "home",
      items: [
        {
          title: "Dashboard",
          href: "/dashboard",
          icon: "home",
          adminOnly: true,
        },
        {
          title: "Home",
          href: "/home",
          icon: "home",
          instructorAccess: true,
          studentAccess: true,
        },
        {
          title: "Minha Agenda",
          href: "/agenda",
          icon: "calendar",
          instructorAccess: true,
          studentAccess: true,
        },
      ],
    },
    {
      title: "Aulas & Turmas",
      icon: "bookOpen",
      items: [
        {
          title: "Turmas",
          href: "/turmas",
          icon: "userGroup",
          adminOnly: false,
          instructorAccess: true,
          studentAccess: false,
        },
        {
          title: "Aulas Livres",
          href: "/aulas/livres",
          icon: "sparkles",
          adminOnly: true,
        },
      ],
    },
    {
      title: "Presenças",
      icon: "checkCircle",
      items: [
        {
          title: "Presenças",
          href: "/presenca",
          icon: "checkCircle",
          adminOnly: false,
          instructorAccess: true,
          studentAccess: false,
        },
        {
          title: "Modo de Recepção",
          href: "#reception-mode",
          icon: "qrCode",
          adminOnly: false,
          instructorAccess: true,
          studentAccess: false,
        }
      ]
    },
    {
      title: "Academia",
      icon: "academicCap",
      items: [
        {
          title: "Instrutores",
          href: "/instrutores",
          icon: "academicCap",
          adminOnly: true,
        },
        {
          title: "Alunos",
          href: "/alunos",
          icon: "users",
          adminOnly: true,
        },
      ],
    },
    {
      title: "Financeiro",
      icon: "banknotes",
      items: [
        {
          title: "Visão Geral",
          href: "/financeiro/dashboard",
          icon: "banknotes",
          adminOnly: true,
        },
        {
          title: "Mensalidades",
          href: "/financeiro/mensalidades",
          icon: "creditCard",
          adminOnly: true,
        },
        {
          title: "Pagamentos",
          href: "/financeiro/pagamentos",
          icon: "dollarSign",
          adminOnly: true,
        },
        {
          title: "Recorrentes e Planos",
          href: "/financeiro/recorrentes",
          icon: "clock",
          adminOnly: true,
        },
        {
          title: "Formas de Pagamento",
          href: "/financeiro/formas-pagamento",
          icon: "wallet",
          adminOnly: true,
        },
        {
          title: "Crescimento",
          href: "/financeiro/crescimento",
          icon: "trendingUp",
          adminOnly: true,
        },
        {
          title: "Configurações",
          href: "/financeiro/configuracoes",
          icon: "settings",
          adminOnly: true,
        },
      ],
    },
    {
      title: "Configurações",
      icon: "settings",
      items: [
        {
          title: "Academia",
          href: "/academia/configuracoes",
          icon: "settings",
          adminOnly: true,
        },
      ],
    },
  ],
  
  routes: {
    public: [
      '/login',
      '/reset-password',
      '/theme-example',
      '/como-configurar-rotas',
      '/api/public',
      '/api/auth/*',
      '/auth/callback'
    ],
    
    protected: [
      '/dashboard',
      '/home',
      '/alunos',
      '/perfil',
      '/instrutores',
      '/aulas',
      '/aulas/*',
      '/aulas/agenda',
      '/turmas',
      '/turmas/*',
      '/aulas/calendario',
      '/aulas/livres',
      '/aulas/livres/*',
      '/presenca',
      '/presenca/*',
      '/aulas/checkin',
      '/aulas/checkin/*',
      '/checkin',
      '/agenda',
      '/financeiro',
      '/financeiro/*',
    ],
    
    auth: [
      '/login',
      '/signup',
      '/forgot-password',
      '/reset-password',
      '/auth/callback'
    ],
    
    bypass: [
      '/theme-example',
      '/theme-example/',
      '/como-configurar-rotas',
      '/como-configurar-rotas/',
      '/_next',
      '/favicon.ico',
      '/api/*',
      '/api/webhook',
      '/api/auth/*',
      '/api/tenant/*',
      '/api/check-favicon',
      '/api/favicon',
      '/api/health'
    ],
    
    isPublic(path: string): boolean {
      if (this.public.includes(path)) {
        return true;
      }
      
      return this.public.some(publicPath => {
        if (publicPath.endsWith('*')) {
          const prefix = publicPath.slice(0, -1);
          return path.startsWith(prefix);
        }
        return false;
      });
    },
    
    isProtected(path: string): boolean {
      if (this.protected.includes(path)) {
        return true;
      }
      
      return this.protected.some(protectedPath => {
        if (protectedPath.endsWith('*')) {
          const prefix = protectedPath.slice(0, -1);
          return path.startsWith(prefix);
        }
        return false;
      });
    },
    
    isAuth(path: string): boolean {
      return this.auth.includes(path);
    },
    
    shouldBypass(path: string): boolean {
      if (this.bypass.includes(path)) {
        return true;
      }
      
      return this.bypass.some(bypassPath => {
        if (bypassPath.endsWith('*')) {
          const prefix = bypassPath.slice(0, -1);
          return path.startsWith(prefix);
        }
        return path.startsWith(bypassPath);
      });
    }
  },
}; 