import { countries } from '@/components/shared/PhoneInput/countries';
import { parsePhoneNumberFromString, type MetadataJson } from 'libphonenumber-js/core'
import customMetadata from '@/lib/metadata.custom.json'

const metadata = customMetadata as any as MetadataJson

/**
 * Representa um número de telefone separado em código do país e número local.
 */
export interface ParsedPhoneNumber {
  countryCode: string;
  nationalNumber: string;
}

/**
 * Analisa uma string de número de telefone para separar o código do país e o número nacional.
 * Lida com números que podem ou não ter um '+' no início.
 * 
 * @param phoneNumber A string completa do número de telefone (ex: "+5511999998888" ou "5511999998888").
 * @param defaultCountryCode O código do país a ser usado se nenhum for encontrado (ex: "+55").
 * @returns Um objeto com countryCode e nationalNumber.
 */
export const parsePhoneNumber = (
  phoneNumber: string,
  defaultCountryCode: string = '+55'
): ParsedPhoneNumber => {
  if (!phoneNumber) {
    return { countryCode: defaultCountryCode, nationalNumber: '' };
  }

  // Normaliza o número para garantir que tenha o '+' se for um código de país válido
  const normalizedPhone = phoneNumber.startsWith('+') ? phoneNumber : `+${phoneNumber}`;

  // Encontra o código de país mais longo que corresponde ao início do número
  const country = countries
    .slice() // Cria uma cópia para não modificar o array original
    .sort((a, b) => b.phoneCode.length - a.phoneCode.length) // Ordena por comprimento desc.
    .find(c => normalizedPhone.startsWith(c.phoneCode));

  if (country) {
    const nationalNumber = normalizedPhone.substring(country.phoneCode.length);
    return {
      countryCode: country.phoneCode,
      nationalNumber: nationalNumber,
    };
  }
  
  // Se nenhum país for encontrado, assume o padrão e retorna o número original (sem o `+` se não estava lá)
  return {
    countryCode: defaultCountryCode,
    nationalNumber: phoneNumber.replace(/\D/g, ''),
  };
};

/**
 * Valida um número de telefone.
 *
 * @param phone O número de telefone a ser validado.
 * @param defaultCountry O país padrão a ser usado se o DDI não for fornecido. Padrão é 'BR'.
 * @returns {boolean} `true` se o número for válido, `false` caso contrário.
 */
export function validatePhoneNumber(
  phone: string,
  defaultCountry: any = 'BR',
): boolean {
  if (!phone) {
    return false
  }

  const phoneNumber = parsePhoneNumberFromString(phone, defaultCountry, metadata)
  return phoneNumber ? phoneNumber.isValid() : false
}

/**
 * Formata um número de telefone para o padrão E.164.
 *
 * @param phone O número de telefone a ser formatado.
 * @param defaultCountry O país padrão a ser usado se o DDI não for fornecido. Padrão é 'BR'.
 * @returns {string | null} O número formatado ou `null` se inválido.
 */
export function formatPhoneNumber(
  phone: string,
  defaultCountry: any = 'BR',
): string | null {
  if (!phone) {
    return null
  }

  const phoneNumber = parsePhoneNumberFromString(phone, defaultCountry, metadata)
  return phoneNumber && phoneNumber.isValid() ? phoneNumber.number : null
}

/**
 * Formata um número de telefone para exibição.
 *
 * @param phone O número de telefone a ser formatado.
 * @param defaultCountry O país padrão a ser usado se o DDI não for fornecido. Padrão é 'BR'.
 * @param format O formato desejado ('international' ou 'national').
 * @returns {string} O número formatado.
 */
export function formatPhoneNumberDisplay(
  phone: string,
  defaultCountry: any = 'BR',
  format: 'international' | 'national' = 'international'
): string {
  if (!phone) {
    return '';
  }

  const phoneNumber = parsePhoneNumberFromString(phone, defaultCountry, metadata);
  if (!phoneNumber || !phoneNumber.isValid()) {
    return phone;
  }

  return format === 'national'
    ? phoneNumber.formatNational()
    : phoneNumber.formatInternational();
}

/**
 * Formata um número de telefone brasileiro para exibição nacional com DDI.
 * Exemplo: '+55 (94) 96988-2841'
 *
 * @param phone Número de telefone em formato variado
 * @param defaultCountry País padrão, BR por padrão
 * @returns {string} Número formatado ou string original se inválido
 */
export function formatPhoneNumberBRWithDDI(
  phone: string,
  defaultCountry: any = 'BR'
): string {
  if (!phone) {
    return '';
  }

  const phoneNumber = parsePhoneNumberFromString(phone, defaultCountry, metadata);
  if (!phoneNumber || !phoneNumber.isValid()) {
    return phone;
  }

  const national = phoneNumber.formatNational();
  const countryCode = `+${phoneNumber.countryCallingCode}`;
  return `${countryCode} ${national}`;
} 