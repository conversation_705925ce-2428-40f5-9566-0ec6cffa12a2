'use server';

import { cache } from 'react';
import { getSupabaseConfig } from '@/config/supabase';

export const getTenantData = cache(async (slug: string) => {
  if (!slug) return null;
  
  try {
    const config = getSupabaseConfig();
    const response = await fetch(
      `${config.url}/rest/v1/tenants?slug=eq.${encodeURIComponent(slug)}&select=name,primary_color,secondary_color,logo_url,description`,
      {
        headers: {
          'apikey': config.anonKey,
          'Content-Type': 'application/json'
        },
        next: { 
          revalidate: 300 // Cache por 5 minutos
        }
      }
    );

    if (!response.ok) return null;
    
    const data = await response.json();
    return data[0] || null;
  } catch (error) {
    console.error('Erro ao recuperar tenant:', error);
    return null;
  }
}); 