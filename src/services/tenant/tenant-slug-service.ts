import { headers, cookies } from 'next/headers';
import { createClient } from '@/services/supabase/server';
import { TenantExtractorServer } from './tenant-extractor-server';
import { cache } from 'react';
import { getSupabaseConfig } from '@/config/supabase';

/**
 * @deprecated Use TenantExtractorServer diretamente para melhor performance
 * Mantido para compatibilidade com código existente
 */
export const extractTenantSlug = cache(async (fallbackSlug = ''): Promise<string> => {
  // Verificar primeiro o cabeçalho definido pelo middleware
  try {
    const headersList = await headers();
    const cookieStore = await cookies();
    const tenantHeader = headersList.get('x-tenant-slug');
    if (tenantHeader) return tenantHeader;

    // ========== MIGRAÇÃO PARA O SISTEMA OTIMIZADO ==========
    // ANTES: const extractor = new TenantExtractor(fallbackSlug);
    // DEPOIS: Usando sistema otimizado
    const extractor = new TenantExtractorServer(fallbackSlug);
    return extractor.extractTenantSlug(headersList, cookieStore);
  } catch (e) {
    // Falha silenciosa se não estiver em um RSC
    const extractor = new TenantExtractorServer(fallbackSlug);
    return extractor.extractTenantSlug();
  }
});

/**
 * @deprecated Use TenantExtractorServer.getTenantInfo() para melhor performance
 * Mantido para compatibilidade com código existente
 */
export const getTenantBySlug = cache(async (slug: string) => {
  if (!slug) return null;
  
  try {
    const extractor = new TenantExtractorServer();
    const supabase = await createClient();
    const tenantInfo = await extractor.getTenantInfo(supabase);
    
    // Converter para o formato esperado pelo código antigo
    if (tenantInfo && tenantInfo.slug === slug) {
      return {
        name: tenantInfo.name,
        primary_color: tenantInfo.primaryColor,
        secondary_color: tenantInfo.secondaryColor,
        logo_url: tenantInfo.logoUrl,
        description: null // Campo não disponível no TenantInfo atual
      };
    }
    
    return null;
  } catch (error) {
    console.error('Erro ao recuperar tenant:', error);
    return null;
  }
});

/**
 * @deprecated Use TenantExtractorServer diretamente para melhor performance
 * Mantido para compatibilidade com código existente
 */
export const getTenantColors = cache(async (): Promise<{
  primaryColor: string | null,
  secondaryColor: string | null,
  logoUrl: string | null,
  tenantName: string | null
}> => {
  try {
    const extractor = new TenantExtractorServer();
    const supabase = await createClient();
    const tenantInfo = await extractor.getTenantInfo(supabase);
    
    return {
      primaryColor: tenantInfo?.primaryColor || null,
      secondaryColor: tenantInfo?.secondaryColor || null,
      logoUrl: tenantInfo?.logoUrl || null,
      tenantName: tenantInfo?.name || null
    };
  } catch (error) {
    console.error('Erro ao obter informações do tenant:', error);
    return {
      primaryColor: null,
      secondaryColor: null,
      logoUrl: null,
      tenantName: null
    };
  }
});

/**
 * Função otimizada para obter informações completas do tenant
 * Esta é a versão recomendada para código novo
 */
export const getTenantInfoOptimized = cache(async () => {
  const extractor = new TenantExtractorServer();
  const supabase = await createClient();
  return extractor.getTenantInfo(supabase);
});

export const getTenantCarouselData = cache(async () => {
  try {
    const slug = await extractTenantSlug();
    if (!slug) return null;
    
    // Usar fetch com cache
    const config = getSupabaseConfig();
    const response = await fetch(
      `${config.url}/rest/v1/tenants?slug=eq.${encodeURIComponent(slug)}&select=description`,
      {
        headers: {
          'apikey': config.anonKey,
          'Content-Type': 'application/json'
        },
        next: { 
          revalidate: 300 // Cache por 5 minutos
        }
      }
    );

    if (!response.ok) return null;
    
    const data = await response.json();
    const tenant = data[0];
    
    if (tenant?.description) {
      try {
        const parsedDescription = JSON.parse(tenant.description);
        if (Array.isArray(parsedDescription) && parsedDescription.length > 0) {
          return parsedDescription;
        }
      } catch (error) {
        console.error('Erro ao fazer parse da descrição do tenant:', error);
      }
    }
    
    return null;
  } catch (error) {
    console.error('Erro ao obter dados do carrossel do tenant:', error);
    return null;
  }
}); 