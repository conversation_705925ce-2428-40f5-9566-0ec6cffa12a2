// Re-exportar tipos
export * from './types';

// Configuração centralizada
export * from './tenant-config';

// Cache otimizado
export * from './tenant-cache';

// Utilitários compartilhados
export * from './tenant-utils';

// Extractor para cliente (safe para Client Components)
export { TenantExtractorClient } from './tenant-extractor-client';

// Compatibilidade com código existente - alias para extractTenantSlug
export { extractTenantSlug as getTenantSlug } from './tenant-slug-service';

// NOTA: Serviços que usam next/headers foram movidos para /server
// Para usar em Server Components/Actions, importe de '@/services/tenant/server'