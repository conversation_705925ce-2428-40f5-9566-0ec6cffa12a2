import { SupabaseClient, createClient } from '@supabase/supabase-js';
import { Database } from '@/services/supabase/types/database.types';
import { cache } from 'react';
import { getSupabaseConfig } from '@/config/supabase';
import { TenantIdentifier, TenantInfo, TenantExtractionContext } from './types';
import { TENANT_CONFIG, TENANT_HEADERS } from './tenant-config';
import { TenantUtils } from './tenant-utils';
import { tenantCache } from './tenant-cache';

export class TenantExtractorServer {
  private readonly defaultTenant: string;

  constructor(defaultTenant = TENANT_CONFIG.defaultTenant) {
    this.defaultTenant = defaultTenant;
  }

  /**
   * Extrai o slug do tenant no servidor (com cache)
   */
  extractTenantSlug = cache(async (headersList?: any, cookieStore?: any): Promise<string> => {
    try {
      const context = await this.buildExtractionContext(headersList, cookieStore);
      const slug = TenantUtils.extractTenantSlug(context);

      return slug || this.defaultTenant;
    } catch (error) {
      console.warn('Erro ao extrair slug do tenant:', error);
      return this.defaultTenant;
    }
  });

  /**
   * Extrai o ID do tenant baseado no slug (com cache)
   */
  extractTenantId = cache(async (slug: string): Promise<string | null> => {
    if (!TenantUtils.isValidSlug(slug)) {
      return null;
    }

    // Verificar cache primeiro
    const cacheKey = `tenant:id:${slug}`;
    const cachedId = tenantCache.get(cacheKey);
    if (cachedId) return cachedId;

    try {
      const supabase = this.createSupabaseClient();
      const { data, error } = await supabase
        .from('tenants')
        .select('id')
        .eq('slug', slug)
        .single();

      if (error || !data) {
        return await this.handleTenantLookupError(supabase, slug, error);
      }

      // Armazenar no cache
      tenantCache.set(cacheKey, data.id);
      return data.id;
    } catch (error) {
      console.warn(`Erro ao buscar ID do tenant para slug ${slug}:`, error);
      return null;
    }
  });

  /**
   * Obtém identificador completo do tenant (com cache)
   */
  getTenantIdentifier = cache(async (headersList?: any, cookieStore?: any): Promise<TenantIdentifier> => {
    const slug = await this.extractTenantSlug(headersList, cookieStore);
    const id = await this.extractTenantId(slug);

    return { id, slug };
  });

  /**
   * Obtém informações completas do tenant (com cache)
   */
  getTenantInfo = cache(async (supabase: SupabaseClient<Database>, headersList?: any, cookieStore?: any): Promise<TenantInfo> => {
    const tenant = await this.getTenantIdentifier(headersList, cookieStore);

    if (!tenant.slug) {
      return this.getEmptyTenantInfo(tenant);
    }

    // Verificar cache primeiro
    const cacheKey = `tenant:info:${tenant.slug}`;
    const cachedInfo = tenantCache.get(cacheKey);
    if (cachedInfo) {
      try {
        return JSON.parse(cachedInfo);
      } catch (e) {
        // Cache corrompido, continuar com a busca
      }
    }

    try {
      const { data } = await supabase
        .from('tenants')
        .select('name, primary_color, secondary_color, logo_url')
        .eq('slug', tenant.slug)
        .single();

      const tenantInfo: TenantInfo = {
        ...tenant,
        name: data?.name || null,
        primaryColor: data?.primary_color || null,
        secondaryColor: data?.secondary_color || null,
        logoUrl: data?.logo_url || null
      };

      // Armazenar no cache
      tenantCache.set(cacheKey, JSON.stringify(tenantInfo));
      
      return tenantInfo;
    } catch (error) {
      console.warn(`Erro ao buscar informações do tenant ${tenant.slug}:`, error);
      return this.getEmptyTenantInfo(tenant);
    }
  });

  /**
   * Constrói o contexto de extração do servidor
   */
  private async buildExtractionContext(headersList?: any, cookieStore?: any): Promise<TenantExtractionContext> {
    // Se não foram fornecidos, importar dinamicamente next/headers
    if (!headersList || !cookieStore) {
      const { headers, cookies } = await import('next/headers');
      const [headersResult, cookiesResult] = await Promise.all([
        headers(),
        cookies()
      ]);
      headersList = headersResult;
      cookieStore = cookiesResult;
    }

    // Extrair headers relevantes
    const extractedHeaders: Record<string, string> = {};
    for (const header of Object.values(TENANT_HEADERS)) {
      const value = headersList.get(header);
      if (value) extractedHeaders[header] = value;
    }

    // Extrair cookies relevantes
    const extractedCookies: Record<string, string> = {};
    const hostCookie = cookieStore.get('host')?.value;
    const tenantCookie = cookieStore.get('x-tenant-slug')?.value;
    
    if (hostCookie) extractedCookies.host = hostCookie;
    if (tenantCookie) extractedCookies['x-tenant-slug'] = tenantCookie;

    // Determinar pathname do header ou URL
    const pathname = this.extractPathnameFromHeaders(headersList);

    return {
      hostname: extractedHeaders.host || extractedHeaders['x-forwarded-host'] || '',
      pathname,
      headers: extractedHeaders,
      cookies: extractedCookies
    };
  }

  /**
   * Extrai pathname dos headers disponíveis
   */
  private extractPathnameFromHeaders(headersList: any): string {
    const possibleSources = [
      'x-url',
      'x-original-uri',
      'x-forwarded-uri',
      'referer'
    ];

    for (const source of possibleSources) {
      const value = headersList.get(source);
      if (value) {
        try {
          if (value.startsWith('http')) {
            return new URL(value).pathname;
          } else if (value.startsWith('/')) {
            return value;
          }
        } catch (e) {
          // Continuar tentando outras fontes
        }
      }
    }

    return '';
  }

  /**
   * Trata erros na busca de tenant
   */
  private async handleTenantLookupError(
    supabase: SupabaseClient<Database>,
    slug: string,
    _error: any
  ): Promise<string | null> {
    if (process.env.NODE_ENV !== 'development') {
      return null;
    }

    return this.fallbackTenantLookup(supabase, slug);
  }

  /**
   * Busca fallback para desenvolvimento
   */
  private async fallbackTenantLookup(
    supabase: SupabaseClient<Database>,
    slug: string
  ): Promise<string | null> {
    try {
      const { data: allTenants } = await supabase
        .from('tenants')
        .select('id, slug, name')
        .limit(5);
        
      if (!allTenants || allTenants.length === 0) {
        return null;
      }
      
      // Tentar buscar por correspondência parcial
      const matchingTenant = allTenants.find(t => 
        t.slug?.toLowerCase() === slug.toLowerCase() || 
        t.name?.toLowerCase() === slug.toLowerCase()
      );
      
      if (matchingTenant) {
        return matchingTenant.id;
      }
      
      // Usar primeiro tenant como fallback se configurado
      if (process.env.NEXT_PUBLIC_TENANT_FALLBACK === 'true') {
        return allTenants[0].id;
      }
    } catch (error) {
      console.warn('Erro no fallback lookup:', error);
    }
    
    return null;
  }

  /**
   * Retorna informações vazias de tenant
   */
  private getEmptyTenantInfo(tenant: TenantIdentifier): TenantInfo {
    return {
      ...tenant,
      name: null,
      primaryColor: null,
      secondaryColor: null,
      logoUrl: null
    };
  }

  /**
   * Cria cliente Supabase
   */
  private createSupabaseClient(): SupabaseClient<Database> {
    const { url, anonKey } = getSupabaseConfig();
    return createClient<Database>(url, anonKey);
  }

  /**
   * Limpa cache de tenant específico
   */
  clearTenantCache(_slug: string): void {
    tenantCache.clear();
  }

  /**
   * Obtém estatísticas do cache
   */
  getCacheStats() {
    return tenantCache.getStats();
  }
} 